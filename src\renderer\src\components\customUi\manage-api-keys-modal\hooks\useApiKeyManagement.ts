import { useState, useEffect, useRef, useCallback } from 'react';
import { RestyleConfig, ProjectSettings } from '@/types/global';
import { ManageApiKeysModalProps } from '../types';
import { useNotification } from '@/contexts/NotificationContext';

interface UseApiKeyManagementProps {
  isOpen: boolean;
  projectSettings?: ProjectSettings;
  onUpdateProjectSettings?: (updatedSettings: ProjectSettings) => Promise<void>;
}

export const useApiKeyManagement = ({
  isOpen,
  projectSettings,
  onUpdateProjectSettings
}: UseApiKeyManagementProps) => {
  const { showNotification } = useNotification();

  // State
  const [editedConfigs, setEditedConfigs] = useState<RestyleConfig[]>([]);
  const [newConfigName, setNewConfigName] = useState('');
  const [configToDelete, setConfigToDelete] = useState<RestyleConfig | null>(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);

  // Initialize edited configs when modal opens or project settings change
  useEffect(() => {
    if (isOpen && projectSettings) {
      console.log('[API Keys Modal] Loading configs from project settings:', projectSettings.restyleConfigs);
      setEditedConfigs([...projectSettings.restyleConfigs]);
    }
  }, [isOpen, projectSettings]);

  // Save project settings immediately (like prompts modal)
  const saveProjectSettings = useCallback(async (updatedConfigs: RestyleConfig[]) => {
    if (!projectSettings || !onUpdateProjectSettings) return;

    try {
      const updatedSettings: ProjectSettings = {
        ...projectSettings,
        restyleConfigs: updatedConfigs
      };
      console.log('[API Keys Modal] Saving updated settings:', updatedSettings);
      await onUpdateProjectSettings(updatedSettings);
      console.log('[API Keys Modal] Settings saved successfully');
    } catch (error) {
      console.error('[API Keys Modal] Error saving API key changes:', error);
      showNotification('Failed to save API key changes.', 'error');
      throw error;
    }
  }, [projectSettings, onUpdateProjectSettings, showNotification]);

  // Save all pending changes (for modal close)
  const saveAllPendingChanges = useCallback(async () => {
    // This is mainly for compatibility, but immediate saves should handle most cases
    await saveProjectSettings(editedConfigs);
  }, [editedConfigs, saveProjectSettings]);

  const saveAllPendingChangesRef = useRef(saveAllPendingChanges);
  saveAllPendingChangesRef.current = saveAllPendingChanges;

  // Update config field
  const handleUpdateConfigField = useCallback(async (
    configToUpdate: RestyleConfig,
    field: keyof RestyleConfig,
    newValue: string
  ) => {
    const updatedConfigs = editedConfigs.map(config =>
      config.id === configToUpdate.id
        ? { ...config, [field]: newValue }
        : config
    );

    setEditedConfigs(updatedConfigs);

    try {
      await saveProjectSettings(updatedConfigs);
      showNotification(`Configuration ${field} updated successfully.`, 'success');
    } catch (error) {
      console.error(`Error updating config ${field}:`, error);
      showNotification(`Failed to update configuration ${field}.`, 'error');
      // Revert on error
      setEditedConfigs(editedConfigs);
    }
  }, [editedConfigs, saveProjectSettings, showNotification]);

  // Handle visibility toggle
  const handleVisibilityToggle = useCallback(async (configId: string, isVisible: boolean) => {
    const updatedConfigs = editedConfigs.map(config =>
      config.id === configId
        ? { ...config, isVisible }
        : config
    );

    setEditedConfigs(updatedConfigs);

    try {
      await saveProjectSettings(updatedConfigs);
      showNotification(`Configuration visibility updated.`, 'success');
    } catch (error) {
      console.error('Error updating config visibility:', error);
      showNotification('Failed to update configuration visibility.', 'error');
      // Revert on error
      setEditedConfigs(editedConfigs);
    }
  }, [editedConfigs, saveProjectSettings, showNotification]);

  // Handle delete initiation
  const handleInitiateDelete = useCallback((config: RestyleConfig) => {
    setConfigToDelete(config);
    setIsDeleteConfirmOpen(true);
  }, []);

  // Handle delete confirmation
  const handleDeleteConfig = useCallback(async () => {
    if (configToDelete) {
      const updatedConfigs = editedConfigs.filter(config => config.id !== configToDelete.id);

      setEditedConfigs(updatedConfigs);
      setConfigToDelete(null);
      setIsDeleteConfirmOpen(false);

      try {
        await saveProjectSettings(updatedConfigs);
        showNotification(`Configuration "${configToDelete.name}" deleted successfully.`, 'success');
      } catch (error) {
        console.error('Error deleting config:', error);
        showNotification('Failed to delete configuration.', 'error');
        // Revert on error
        setEditedConfigs(editedConfigs);
      }
    }
  }, [configToDelete, editedConfigs, saveProjectSettings, showNotification]);

  // Move config (for drag and drop)
  const moveConfig = useCallback(async (dragIndex: number, hoverIndex: number) => {
    const draggedConfig = editedConfigs[dragIndex];
    const newConfigs = [...editedConfigs];
    newConfigs.splice(dragIndex, 1);
    newConfigs.splice(hoverIndex, 0, draggedConfig);

    setEditedConfigs(newConfigs);

    try {
      await saveProjectSettings(newConfigs);
    } catch (error) {
      console.error('Error reordering configs:', error);
      showNotification('Failed to reorder configurations.', 'error');
      // Revert on error
      setEditedConfigs(editedConfigs);
    }
  }, [editedConfigs, saveProjectSettings, showNotification]);

  // Create new config
  const handleCreateNewConfig = useCallback(async () => {
    if (!newConfigName.trim()) {
      showNotification('Please enter a configuration name.', 'error');
      return;
    }

    const newConfig: RestyleConfig = {
      id: `config_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: newConfigName.trim(),
      apiUrl: '',
      apiKey: '',
      isVisible: true
    };

    const updatedConfigs = [...editedConfigs, newConfig];

    setEditedConfigs(updatedConfigs);
    setNewConfigName('');

    try {
      await saveProjectSettings(updatedConfigs);
      showNotification(`Configuration "${newConfig.name}" created successfully.`, 'success');
    } catch (error) {
      console.error('Error creating config:', error);
      showNotification('Failed to create configuration.', 'error');
      // Revert on error
      setEditedConfigs(editedConfigs);
      setNewConfigName(newConfigName);
    }
  }, [newConfigName, editedConfigs, saveProjectSettings, showNotification]);

  return {
    // State
    editedConfigs,
    setEditedConfigs,
    newConfigName,
    setNewConfigName,
    configToDelete,
    setConfigToDelete,
    isDeleteConfirmOpen,
    setIsDeleteConfirmOpen,

    // Handlers
    handleUpdateConfigField,
    handleCreateNewConfig,
    handleVisibilityToggle,
    handleInitiateDelete,
    handleDeleteConfig,
    moveConfig,
    saveAllPendingChanges,
    saveAllPendingChangesRef
  };
};
