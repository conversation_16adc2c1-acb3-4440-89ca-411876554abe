// src/contexts/ThemeContext.tsx
import React, { createContext, useState, useEffect, useCallback, ReactNode } from 'react';
import {
  THEME_STORAGE_KEY,
  APP_LOGO_COLORS_STORAGE_KEY,
  LIGHT_MODE_STROKE_COLOR,
  DARK_MODE_STROKE_COLOR,
  lightModeColorSchemes,
  darkModeColorSchemes,
  LogoColors,
  ColorScheme
} from './../lib/constants';

// Theme options (add new themes here)
export type ThemeMode = 'light' | 'dark' | 'custom';

interface ThemeContextType {
  theme: ThemeMode;
  isDarkMode: boolean; // Maintained for compatibility
  logoColors: LogoColors;
  toggleTheme: () => void;
  setTheme: (theme: ThemeMode) => void;
}

export const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Helper to determine if a theme is effectively dark (for color calculations)
const isEffectivelyDarkTheme = (theme: ThemeMode): boolean => {
  return theme === 'dark' || (theme === 'custom' && window.matchMedia('(prefers-color-scheme: dark)').matches);
};

const selectRandomScheme = (theme: ThemeMode): LogoColors => {
  const isDark = isEffectivelyDarkTheme(theme);
  const schemes = isDark ? darkModeColorSchemes : lightModeColorSchemes;
  const randomBase: ColorScheme = schemes[Math.floor(Math.random() * schemes.length)] || schemes[0];
  return {
    fill: randomBase.body,
    eyes: randomBase.eyes,
    stroke: isDark ? DARK_MODE_STROKE_COLOR : LIGHT_MODE_STROKE_COLOR,
  };
};

export const ThemeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Initialize theme from localStorage or system preference
  const [theme, setThemeState] = useState<ThemeMode>(() => {
    const storedTheme = localStorage.getItem(THEME_STORAGE_KEY) as ThemeMode | null;
    return storedTheme || 'light';
  });

  // For backwards compatibility
  const isDarkMode = isEffectivelyDarkTheme(theme);

  const [logoColors, setLogoColors] = useState<LogoColors>(() => {
    const storedColorsJSON = localStorage.getItem(APP_LOGO_COLORS_STORAGE_KEY);
    if (storedColorsJSON) {
      try {
        const storedColors = JSON.parse(storedColorsJSON) as LogoColors;
        // Ensure the stored colors match the current theme
        const currentThemeIsDark = isEffectivelyDarkTheme(theme);
        const expectedStroke = currentThemeIsDark ? DARK_MODE_STROKE_COLOR : LIGHT_MODE_STROKE_COLOR;
        if (storedColors.stroke === expectedStroke) {
          return storedColors;
        }
      } catch (e) {
        console.error("Error parsing stored logo colors:", e);
      }
    }
    return selectRandomScheme(theme);
  });

  // Apply theme to document
  useEffect(() => {
    // Remove all theme classes first
    document.documentElement.classList.remove('light', 'dark', 'custom');

    // Add the current theme class
    document.documentElement.classList.add(theme);

    // Store in localStorage
    localStorage.setItem(THEME_STORAGE_KEY, theme);

    // Update the color scheme meta tag
    if (theme === 'dark') {
      document.documentElement.style.colorScheme = 'dark';
    } else if (theme === 'light') {
      document.documentElement.style.colorScheme = 'light';
    } else {
      // For custom theme, respect system preference
      document.documentElement.style.colorScheme = 'light dark';
    }
  }, [theme]);

  useEffect(() => {
    try {
      localStorage.setItem(APP_LOGO_COLORS_STORAGE_KEY, JSON.stringify(logoColors));
    } catch (error) {
      console.error('Failed to save logo colors to localStorage:', error);
    }
  }, [logoColors]);

  // Listen for changes from other tabs/windows
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === THEME_STORAGE_KEY && event.newValue) {
        const newTheme = event.newValue as ThemeMode;
        if (newTheme !== theme) {
          setThemeState(newTheme);
          setLogoColors(selectRandomScheme(newTheme));
        }
      } else if (event.key === APP_LOGO_COLORS_STORAGE_KEY && event.newValue) {
        try {
          const newColors = JSON.parse(event.newValue) as LogoColors;
          // Check if newColors are valid for the current theme
          const currentThemeIsDark = isEffectivelyDarkTheme(theme);
          const expectedStroke = currentThemeIsDark ? DARK_MODE_STROKE_COLOR : LIGHT_MODE_STROKE_COLOR;
          if (newColors.stroke === expectedStroke) {
            setLogoColors(newColors);
          }
        } catch (error) {
          console.error('Failed to parse logo colors from storage event:', error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [theme]);

  // For system color scheme changes when using 'custom' theme
  useEffect(() => {
    if (theme === 'custom') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

      const handleChange = () => {
        // Update logo colors when system preference changes
        setLogoColors(selectRandomScheme(theme));
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [theme]);

  // Legacy toggle function (cycles between light and dark)
  const toggleTheme = useCallback(() => {
    setThemeState(prevTheme => {
      // Simple toggle between light and dark
      const newTheme = prevTheme === 'light' ? 'dark' : 'light';
      setLogoColors(selectRandomScheme(newTheme));
      return newTheme;
    });
  }, []);

  // New function to set a specific theme
  const setTheme = useCallback((newTheme: ThemeMode) => {
    setThemeState(newTheme);
    setLogoColors(selectRandomScheme(newTheme));
  }, []);

  return (
    <ThemeContext.Provider value={{
      theme,
      isDarkMode, // Keep for backwards compatibility
      logoColors,
      toggleTheme,
      setTheme
    }}>
      {children}
    </ThemeContext.Provider>
  );
};
