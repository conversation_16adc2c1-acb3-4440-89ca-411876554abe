import { useCallback } from 'react';
import { ProjectSettings, DocumentSettings, SystemPrompt } from '@/types/global';
import { useNotification } from '@/contexts/NotificationContext';

interface UsePromptOperationsProps {
  editedGlobalPrompts: SystemPrompt[];
  setEditedGlobalPrompts: (prompts: SystemPrompt[]) => void;
  editedLocalPrompts: SystemPrompt[];
  setEditedLocalPrompts: (prompts: SystemPrompt[]) => void;
  projectSettings?: ProjectSettings;
  activeDocumentSettings?: DocumentSettings | null;
  activeDocumentId: string | null;
  onUpdateProjectSettings?: (updatedSettings: ProjectSettings) => Promise<void>;
  onUpdateActiveDocumentSettings?: (updatedSettings: DocumentSettings) => Promise<void>;
}

export const usePromptOperations = ({
  editedGlobalPrompts,
  setEditedGlobalPrompts,
  editedLocalPrompts,
  setEditedLocalPrompts,
  projectSettings,
  activeDocumentSettings,
  activeDocumentId,
  onUpdateProjectSettings,
  onUpdateActiveDocumentSettings
}: UsePromptOperationsProps) => {
  const { showNotification } = useNotification();

  const handleDeletePrompt = useCallback(async (prompt: SystemPrompt) => {
    const isLocalPrompt = prompt.scope === 'local';

    if (isLocalPrompt) {
      // Delete local prompt
      const updatedLocalPrompts = editedLocalPrompts.filter(p => p.id !== prompt.id);
      setEditedLocalPrompts(updatedLocalPrompts);

      try {
        if (activeDocumentSettings && onUpdateActiveDocumentSettings) {
          // Update selected prompt ID if it's being deleted
          const newDocSettings = { ...activeDocumentSettings };

          if (newDocSettings.selectedSystemPromptId === prompt.id) {
            newDocSettings.selectedSystemPromptId = null;
            newDocSettings.systemPromptContentOverride = undefined;
          }

          newDocSettings.localSystemPrompts = updatedLocalPrompts.map(p => {
            const { scope, ...rest } = p;
            return rest;
          });

          await onUpdateActiveDocumentSettings(newDocSettings);
          showNotification(`Deleted local prompt: ${prompt.name}`, "success");
        }
      } catch (error) {
        console.error("Error deleting local prompt:", error);
        showNotification("Failed to delete local prompt.", "error");
      }
    } else {
      // Delete global prompt
      const updatedGlobalPrompts = editedGlobalPrompts.filter(p => p.id !== prompt.id);
      setEditedGlobalPrompts(updatedGlobalPrompts);

      try {
        // Check if the prompt is used in any document first
        if (activeDocumentSettings && activeDocumentSettings.selectedSystemPromptId === prompt.id && onUpdateActiveDocumentSettings) {
          // If active document is using this prompt, update it
          const newDocSettings = { ...activeDocumentSettings };
          newDocSettings.selectedSystemPromptId = null;
          newDocSettings.systemPromptContentOverride = undefined;

          await onUpdateActiveDocumentSettings(newDocSettings);
        }

        // Update the visible prompts list if needed
        if (activeDocumentSettings && activeDocumentSettings.visibleSystemPromptIds?.includes(prompt.id) && onUpdateActiveDocumentSettings) {
          const updatedVisibleIds = activeDocumentSettings.visibleSystemPromptIds.filter(id => id !== prompt.id);
          await onUpdateActiveDocumentSettings({ ...activeDocumentSettings, visibleSystemPromptIds: updatedVisibleIds });
        }

        if (projectSettings && onUpdateProjectSettings) {
          await onUpdateProjectSettings({
            ...projectSettings,
            systemPrompts: updatedGlobalPrompts.map(p => {
              const { scope, ...rest } = p;
              return rest;
            })
          });
          showNotification(`Deleted prompt: ${prompt.name}`, "success");
        }
      } catch (error) {
        console.error("Error deleting prompt:", error);
        showNotification("Failed to delete prompt.", "error");
      }
    }
  }, [editedGlobalPrompts, setEditedGlobalPrompts, editedLocalPrompts, setEditedLocalPrompts, projectSettings, activeDocumentSettings, onUpdateProjectSettings, onUpdateActiveDocumentSettings, showNotification]);

  const handleVisibilityToggle = useCallback(async (promptId: string, isVisible: boolean) => {
    if (!activeDocumentId || !activeDocumentSettings) {
      showNotification("No active document to update visibility for.", "error");
      return;
    }

    const currentVisibleIds = activeDocumentSettings.visibleSystemPromptIds || [];
    let updatedVisibleIds: string[];

    if (isVisible && !currentVisibleIds.includes(promptId)) {
      updatedVisibleIds = [...currentVisibleIds, promptId];
    } else if (!isVisible && currentVisibleIds.includes(promptId)) {
      updatedVisibleIds = currentVisibleIds.filter(id => id !== promptId);
    } else {
      return; // No change needed
    }

    try {
      if (onUpdateActiveDocumentSettings) {
        await onUpdateActiveDocumentSettings({ ...activeDocumentSettings, visibleSystemPromptIds: updatedVisibleIds });
        showNotification(
          isVisible ? "Prompt added to document dropdown." : "Prompt removed from document dropdown.",
          "success"
        );
      }
    } catch (error) {
      console.error("Error updating prompt visibility:", error);
      showNotification("Failed to update prompt visibility.", "error");
    }
  }, [activeDocumentId, activeDocumentSettings, onUpdateActiveDocumentSettings, showNotification]);

  // Drag and drop handlers
  const moveGlobalPrompt = useCallback((dragIndex: number, hoverIndex: number) => {
    const draggedPrompt = editedGlobalPrompts[dragIndex];
    const updatedPrompts = [...editedGlobalPrompts];
    updatedPrompts.splice(dragIndex, 1);
    updatedPrompts.splice(hoverIndex, 0, draggedPrompt);
    setEditedGlobalPrompts(updatedPrompts);

    // Persist order change
    if (projectSettings && onUpdateProjectSettings) {
      onUpdateProjectSettings({
        ...projectSettings,
        systemPrompts: updatedPrompts.map(p => {
          const { scope, ...rest } = p;
          return rest;
        })
      });
    }
  }, [editedGlobalPrompts, setEditedGlobalPrompts, projectSettings, onUpdateProjectSettings]);

  const moveLocalPrompt = useCallback((dragIndex: number, hoverIndex: number) => {
    const draggedPrompt = editedLocalPrompts[dragIndex];
    const updatedPrompts = [...editedLocalPrompts];
    updatedPrompts.splice(dragIndex, 1);
    updatedPrompts.splice(hoverIndex, 0, draggedPrompt);
    setEditedLocalPrompts(updatedPrompts);

    // Persist order change
    if (activeDocumentSettings && onUpdateActiveDocumentSettings) {
      onUpdateActiveDocumentSettings({
        ...activeDocumentSettings,
        localSystemPrompts: updatedPrompts.map(p => {
          const { scope, ...rest } = p;
          return rest;
        })
      });
    }
  }, [editedLocalPrompts, setEditedLocalPrompts, activeDocumentSettings, onUpdateActiveDocumentSettings]);

  return {
    handleDeletePrompt,
    handleVisibilityToggle,
    moveGlobalPrompt,
    moveLocalPrompt
  };
};
