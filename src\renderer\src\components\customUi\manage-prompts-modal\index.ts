// Main component export for backward compatibility
export { ManagePromptsModalContainer as ManagePromptsModal } from './ManagePromptsModalContainer';

// Type exports
export type {
  ManagePromptsModalProps,
  DraggablePromptItemProps,
  ExpandedEditorProps,
  TabContentProps,
  PromptActionHandlers,
  DeleteConfirmDialogProps
} from './types';

// Hook exports (in case you want to use them elsewhere)
export { usePromptManagement } from './hooks/usePromptManagement';
export { usePromptActions } from './hooks/usePromptActions';
export { usePromptOperations } from './hooks/usePromptOperations';

// Component exports (in case you want to use individual components)
export { DraggablePromptItem } from './components/DraggablePromptItem';
export { ExpandedEditor } from './components/ExpandedEditor';
export { GlobalPromptsTab } from './components/GlobalPromptsTab';
export { DocumentPromptsTab } from './components/DocumentPromptsTab';
export { DeleteConfirmDialog } from './components/DeleteConfirmDialog';

// Constants
export { DND_ITEM_TYPE_PROMPT } from './types';
