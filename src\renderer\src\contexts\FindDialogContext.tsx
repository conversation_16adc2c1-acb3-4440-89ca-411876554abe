import React, { createContext, useContext, useState, ReactNode } from 'react';

interface FindDialogContextType {
  isFindDialogOpen: boolean;
  setIsFindDialogOpen: (open: boolean) => void;
}

const FindDialogContext = createContext<FindDialogContextType | undefined>(undefined);

export const FindDialogProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isFindDialogOpen, setIsFindDialogOpen] = useState(false);

  return (
    <FindDialogContext.Provider value={{ isFindDialogOpen, setIsFindDialogOpen }}>
      {children}
    </FindDialogContext.Provider>
  );
};

export const useFindDialog = () => {
  const context = useContext(FindDialogContext);
  if (context === undefined) {
    throw new Error('useFindDialog must be used within a FindDialogProvider');
  }
  return context;
};
