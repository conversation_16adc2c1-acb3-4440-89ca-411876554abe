import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/ui/dropdown-menu';
import { Download, Info, RefreshCw } from 'lucide-react';

interface UpdateStatusProps {
  className?: string;
}

export const UpdateStatus: React.FC<UpdateStatusProps> = ({ className }) => {
  const [currentVersion, setCurrentVersion] = useState<string>('');
  const [isCheckingForUpdates, setIsCheckingForUpdates] = useState(false);

  useEffect(() => {
    // Get current version on component mount
    const getCurrentVersion = async () => {
      try {
        if (window.app?.getVersion) {
          const version = await window.app.getVersion();
          setCurrentVersion(version);
        }
      } catch (error) {
        console.error('Failed to get current version:', error);
      }
    };

    getCurrentVersion();
  }, []);

  const handleCheckForUpdates = async () => {
    setIsCheckingForUpdates(true);
    try {
      if (window.app?.checkForUpdates) {
        await window.app.checkForUpdates();
        // Note: The actual update check result is handled by the main process
        // and shown via native dialogs
      }
    } catch (error) {
      console.error('Failed to check for updates:', error);
    } finally {
      setIsCheckingForUpdates(false);
    }
  };

  return (
    <TooltipProvider>
      <DropdownMenu>
        <Tooltip>
          <TooltipTrigger asChild>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className={className}
                disabled={isCheckingForUpdates}
              >
                {isCheckingForUpdates ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <Info className="h-4 w-4" />
                )}
              </Button>
            </DropdownMenuTrigger>
          </TooltipTrigger>
          <TooltipContent>
            <p>App version and updates</p>
          </TooltipContent>
        </Tooltip>

        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuItem disabled className="flex flex-col items-start">
            <span className="font-medium">TruffleNote</span>
            <span className="text-xs text-muted-foreground">
              Version {currentVersion || 'Unknown'}
            </span>
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <DropdownMenuItem
            onClick={handleCheckForUpdates}
            disabled={isCheckingForUpdates}
          >
            <Download className="mr-2 h-4 w-4" />
            {isCheckingForUpdates ? 'Checking...' : 'Check for Updates'}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </TooltipProvider>
  );
};

export default UpdateStatus;
