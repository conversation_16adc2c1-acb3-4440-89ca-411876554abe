{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "esModuleInterop": true, "skipLibCheck": true, "strict": false, "noEmit": true, "baseUrl": "."}, "include": ["src/main/**/*.ts", "src/preload/**/*.ts", "src/main/**/*.js", "src/preload/**/*.js", "src/main/**/*.d.ts"]}