import React from 'react';
import { TabButtonsProps } from '../types';
import { cn } from '../utils';

export const TabButtons = React.memo(({ selectedTab, setSelectedTab }: TabButtonsProps) => {
  return (
    <div className="flex border-b border-gray-200 dark:border-gray-800">
      <button
        className={cn(
          'flex-1 py-2 text-xs font-medium',
          selectedTab === 'files'
            ? 'text-primary border-b-2 border-primary'
            : 'text-muted-foreground hover:text-foreground'
        )}
        onClick={() => setSelectedTab('files')}
      >
        Files
      </button>

      <button
        className={cn(
          'flex-1 py-2 text-xs font-medium',
          selectedTab === 'recent'
            ? 'text-primary border-b-2 border-primary'
            : 'text-muted-foreground hover:text-foreground'
        )}
        onClick={() => setSelectedTab('recent')}
      >
        Recent
      </button>

      <button
        className={cn(
          'flex-1 py-2 text-xs font-medium',
          selectedTab === 'bookmarks'
            ? 'text-primary border-b-2 border-primary'
            : 'text-muted-foreground hover:text-foreground'
        )}
        onClick={() => setSelectedTab('bookmarks')}
      >
        Bookmarks
      </button>
    </div>
  );
});

TabButtons.displayName = 'TabButtons';
