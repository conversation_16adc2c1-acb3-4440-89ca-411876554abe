import React from 'react';
import { FileText, File } from 'lucide-react';

interface FileTypeIconProps {
  fileName: string;
}

// Helper component for file type icons - Extracted as a separate component and memoized
export const FileTypeIcon = React.memo(({ fileName }: FileTypeIconProps) => {
  const extension = fileName.split('.').pop()?.toLowerCase();

  switch(extension) {
    case 'md':
      return <FileText className="h-4 w-4 shrink-0" />;
    case 'json':
      return <File className="h-4 w-4 shrink-0" />;
    default:
      return <File className="h-4 w-4 shrink-0" />;
  }
});

FileTypeIcon.displayName = 'FileTypeIcon';
