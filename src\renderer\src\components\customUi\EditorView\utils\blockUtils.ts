import { Block, BlockNoteEditor } from '@blocknote/core';

/**
 * Check if block contains any part of the pasted text
 */
export function blockContainsText(block: Block, pastedText: string): boolean {
  if (!block.content || !Array.isArray(block.content)) return false;
  
  const blockText = block.content
    .filter(item => item.type === 'text')
    .map(item => (item as any).text || '') // Ensure text exists and default to empty string
    .join('')
    .toLowerCase();
    
  if (!pastedText || pastedText.trim().length === 0) return false;

  // Check if block contains any significant portion of pasted text
  const pastedWords = pastedText.toLowerCase().split(/\s+/).filter(word => word.length > 2);
  
  if (pastedWords.length === 0) {
    // If pastedText is short and has no words > 2 chars, check for direct inclusion of the trimmed pasted text
    return blockText.includes(pastedText.toLowerCase().trim());
  }
  return pastedWords.some(word => blockText.includes(word));
}

/**
 * Detects which blocks were affected by a paste operation
 */
export function detectAffectedBlocks(
  prePasteBlocks: Block[], 
  postPasteBlocks: Block[], 
  pastedText: string,
  editor: BlockNoteEditor // Added editor argument for fallback
): Block[] {
  console.log('[detectAffectedBlocks] 🔍 Analyzing block changes...');
  
  const preBlockIds = new Set(prePasteBlocks.map(b => b.id));
  const affectedBlocksSet = new Set<Block>(); // Use a Set to avoid duplicate blocks
  
  postPasteBlocks.forEach((block) => {
    const isNewBlock = !preBlockIds.has(block.id);
    const containsPastedContent = blockContainsText(block, pastedText);
    
    if (isNewBlock) {
      // New blocks that seem to contain pasted content are prime candidates
      if (containsPastedContent) {
         console.log('[detectAffectedBlocks] ✨ New block with pasted content detected:', block.id.substring(0, 8));
         affectedBlocksSet.add(block);
      } else {
         // Consider new empty blocks or blocks whose content might not directly match via blockContainsText (e.g. image block from HTML paste)
         // For now, we are more conservative; if hyperlink restoration is for text, this is likely fine.
         console.log('[detectAffectedBlocks] ✨ New block (content not directly matched by pastedText):', block.id.substring(0, 8));
         // Optionally, add all new blocks if the paste operation is expected to create them regardless of text match
         // affectedBlocksSet.add(block); 
      }
    } else {
      // For existing blocks, check if their content has changed AND seems related to pasted text
      const prePasteBlock = prePasteBlocks.find(b => b.id === block.id);
      const contentChanged = JSON.stringify(prePasteBlock?.content) !== JSON.stringify(block.content);
      if (contentChanged && containsPastedContent) {
        console.log('[detectAffectedBlocks] 📝 Modified existing block with pasted content:', block.id.substring(0, 8));
        affectedBlocksSet.add(block);
      }
    }
  });
  
  let finalAffectedBlocks = Array.from(affectedBlocksSet);

  // Fallback: if no blocks detected but we know content was pasted and editor is available
  if (finalAffectedBlocks.length === 0 && pastedText.trim() && editor) {
    console.log('[detectAffectedBlocks] 🎯 Fallback: No specific new/modified blocks with pasted text found. Attempting to use cursor block.');
    const cursorPosition = editor.getTextCursorPosition();
    if (cursorPosition && cursorPosition.block) {
      // Only add if it's not already there (though Set handles duplicates)
      if (!affectedBlocksSet.has(cursorPosition.block)) {
         console.log('[detectAffectedBlocks] 🎯 Using cursor block as fallback:', cursorPosition.block.id.substring(0,8));
         finalAffectedBlocks.push(cursorPosition.block); 
      }
    } else {
      console.warn('[detectAffectedBlocks] ⚠️ Fallback failed: No cursor block found.');
    }
  }
  
  return finalAffectedBlocks;
}