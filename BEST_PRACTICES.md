# React + Electron + Tailwind + shadcn/ui Best Practices

This document outlines best practices for developing applications using React, Electron, Tailwind CSS, and shadcn/ui components.

## React Best Practices

1. **Component Structure**:
   - Keep components small and focused on a single responsibility
   - Use functional components with hooks instead of class components
   - Use `React.memo()` for performance optimization when needed
   - Define clear prop interfaces with TypeScript

2. **State Management**:
   - Use local state with `useState` for component-specific state
   - Use context for sharing state between closely related components
   - Consider Redux or other state management libraries for complex global state

3. **Error Handling**:
   - Use Error Boundaries to catch errors in component trees
   - Provide fallback UIs when components fail
   - Implement proper error logging

4. **Performance**:
   - Avoid unnecessary re-renders using `useMemo` and `useCallback`
   - Use virtualization for long lists (react-window, react-virtualized)
   - Split code using dynamic imports and React.lazy

## shadcn/ui Component Best Practices

1. **Component Nesting**:
   - ⚠️ **Avoid nesting multiple components with `asChild` prop** - this causes "React.Children.only" errors
   - When using `asChild`, ensure the component has exactly one child element
   - For complex combinations, separate the components or use different patterns

2. **Component Props**:
   - Always provide all required props to shadcn components
   - Use TypeScript to ensure type safety of props
   - Follow the component documentation for proper usage

3. **Styling and Customization**:
   - Use the `cn()` utility for conditional class names
   - Customize themes through CSS variables rather than direct overrides
   - Follow the shadcn component patterns for consistency

## Electron Best Practices

1. **IPC Communication**:
   - Use typed IPC channels for communication between main and renderer processes
   - Implement proper error handling for IPC calls
   - Avoid synchronous IPC when possible

2. **Security**:
   - Follow the principle of least privilege
   - Enable context isolation and node integration off
   - Use a preload script to expose only necessary APIs

3. **Performance**:
   - Minimize main thread blocking
   - Use web workers for CPU-intensive tasks
   - Implement proper memory management to avoid leaks

## Tailwind CSS Best Practices

1. **Organization**:
   - Use consistent naming conventions
   - Extract common patterns into components
   - Use @apply in CSS files for complex, reusable styles

2. **Dark Mode**:
   - Use `dark:` variant for dark mode styles
   - Implement a theme toggle with proper system preference detection
   - Test both light and dark themes

3. **Responsive Design**:
   - Use mobile-first approach with responsive classes
   - Test on multiple screen sizes
   - Use appropriate breakpoints consistently

## Code Quality

1. **TypeScript**:
   - Define proper interfaces for all props and state
   - Avoid using `any` type
   - Enable strict mode in tsconfig.json

2. **Testing**:
   - Write unit tests for components and utilities
   - Use React Testing Library for component testing
   - Implement E2E tests for critical workflows

3. **Code Structure**:
   - Follow consistent file organization
   - Use feature-based or component-based folder structure
   - Keep related code together

## Debugging Tips

1. **Common Issues**:
   - React.Children.only errors: Check for multiple children in components with asChild prop
   - useEffect dependency warnings: Ensure all dependencies are properly listed
   - State update on unmounted component: Use cleanup functions in useEffect

2. **Tools**:
   - Use React DevTools for component inspection
   - Use Redux DevTools for state management debugging
   - Implement proper logging for Electron main process

## Final Recommendations

- Document your component APIs
- Create shared utilities for common operations
- Regularly audit and update dependencies
- Establish coding standards for the team
