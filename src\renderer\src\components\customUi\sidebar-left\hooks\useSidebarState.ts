import { useState, useEffect, useCallback, useMemo } from 'react';
import { FileNode, SelectedTab, SortOrder } from '../types';
import { FrontendStoredFile } from '@/types/global';

export const useSidebarState = (files: FrontendStoredFile[]) => {
  // Component state
  const [expanded, setExpanded] = useState(false);
  const [isPinned, setIsPinned] = useState(false);
  const [fileTree, setFileTree] = useState<FileNode[]>([]);
  const [selectedTab, setSelectedTab] = useState<SelectedTab>('files');
  const [searchTerm, setSearchTerm] = useState('');
  const [expandAll, setExpandAll] = useState(false);
  const [isUiExtensionOpen, setIsUiExtensionOpen] = useState(false);
  const [sortOrder, setSortOrder] = useState<SortOrder>('name');
  const [leaveTimeoutId, setLeaveTimeoutId] = useState<NodeJS.Timeout | null>(null);
  const [bookmarkedFiles, setBookmarkedFiles] = useState<FileNode[]>([]);
  const [recentFiles, setRecentFiles] = useState<FileNode[]>([]);
  const [loading, setLoading] = useState(false);

  // Dialog states
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState<FileNode | null>(null);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [renameTarget, setRenameTarget] = useState<FileNode | null>(null);
  const [newNameInput, setNewNameInput] = useState('');
  const [newFileDialogOpen, setNewFileDialogOpen] = useState(false);
  const [newFolderDialogOpen, setNewFolderDialogOpen] = useState(false);
  const [newItemParentPath, setNewItemParentPath] = useState('');
  const [newItemName, setNewItemName] = useState('');

  // Load persisted sidebar state
  useEffect(() => {
    const storedPin = localStorage.getItem('sidebar-pinned');
    if (storedPin) {
      const isPinned = JSON.parse(storedPin);
      setIsPinned(isPinned);
      if (isPinned) {
        setExpanded(true);
      }
    }
  }, []);

  // Memoize the file tree structure based on the flat files array
  const hierarchicalFileTree = useMemo(() => {
    // Create a map for quick lookup of items by ID
    const itemMap = new Map<string, FileNode>();
    const rootItems: FileNode[] = [];

    // Convert flat items to FileNode structure and populate map
    files.forEach(file => {
      const fileNode: FileNode = {
        id: file.id,
        name: file.name,
        type: file.type === 'document' ? 'file' : 'folder', // Map 'document' to 'file' for FileNode type
        children: [],
        parentId: file.parentId,
        isExpanded: false, // Default to collapsed
        updatedAt: file.updatedAt, // Carry over updatedAt
      };
      itemMap.set(file.id, fileNode);
    });

    // Build the tree structure using parentId
    itemMap.forEach(item => {
      if (item.parentId === null) {
        rootItems.push(item);
      } else {
        const parent = itemMap.get(item.parentId);
        if (parent) {
          // Ensure parent is a folder type before adding a child
          if (parent.type === 'folder') {
              parent.children.push(item);
          } else {
              console.warn(`Item ${item.name} (ID: ${item.id}) has a parentId (${item.parentId}) that is not a folder.`);
              // Optionally, add orphan items to the root
               rootItems.push(item); // Or handle as an error
          }
        } else {
          console.warn(`Item ${item.name} (ID: ${item.id}) has a parentId (${item.parentId}) but no matching parent found. Adding to root.`);
          rootItems.push(item); // Add orphan items to the root
        }
      }
    });

    // Sort children for consistent display (e.g., folders first, then files, then alphabetically)
    const sortTree = (nodes: FileNode[]) => {
        nodes.sort((a, b) => {
            // Primary sort: by type (folders first)
            if (a.type === 'folder' && b.type !== 'folder') return -1;
            if (a.type !== 'folder' && b.type === 'folder') return 1;

            // Secondary sort: based on sortOrder state
            if (sortOrder === 'modified') {
                // Sort by date (descending, newest first)
                const dateA = a.updatedAt ? new Date(a.updatedAt).getTime() : 0;
                const dateB = b.updatedAt ? new Date(b.updatedAt).getTime() : 0;
                if (dateB !== dateA) return dateB - dateA;
                // If dates are same, fall back to name for consistent ordering
                return a.name.localeCompare(b.name);
            } else { // Default to 'name' sort
                return a.name.localeCompare(b.name);
            }
        });
        nodes.forEach(node => {
            if (node.children.length > 0) {
                sortTree(node.children);
            }
        });
    };

    sortTree(rootItems);

    return rootItems;
  }, [files, sortOrder]); // Recalculate when the flat files array or sortOrder changes

  // Update fileTree state when hierarchicalFileTree changes
  useEffect(() => {
    setFileTree(hierarchicalFileTree);
  }, [hierarchicalFileTree]);

  // Toggle sidebar expansion
  const toggleExpanded = useCallback(() => {
    if (!isPinned || (isPinned && expanded)) {
      setExpanded(prev => !prev);
    }
  }, [isPinned, expanded]);

  // Toggle sidebar pin state
  const togglePin = useCallback(() => {
    const newPinState = !isPinned;
    setIsPinned(newPinState);
    localStorage.setItem('sidebar-pinned', JSON.stringify(newPinState));
  }, [isPinned]);

  // Handle mouse enter on sidebar or hover strip
  const handleMouseEnterSidebar = useCallback(() => {
    if (leaveTimeoutId) {
      clearTimeout(leaveTimeoutId);
      setLeaveTimeoutId(null);
    }
  }, [leaveTimeoutId]);

  // Handle mouse enter on the hover strip
  const handleHoverStripEnter = useCallback(() => {
    handleMouseEnterSidebar();
    if (!isPinned && !expanded) {
      setExpanded(true);
    }
  }, [handleMouseEnterSidebar, isPinned, expanded]);

  // Handle mouse leave from sidebar
  const handleSidebarLeave = useCallback(() => {
    // Don't start timeout if UI extension (modal/dropdown) is open
    if (isUiExtensionOpen) {
      return;
    }
    if (leaveTimeoutId) clearTimeout(leaveTimeoutId);

    const newTimeoutId = setTimeout(() => {
      // Double-check that UI extension is still not open before closing
      if (!isPinned && expanded && !isUiExtensionOpen) {
        setExpanded(false);
      }
      setLeaveTimeoutId(null); // Clear the timeout ID after execution
    }, 300);

    setLeaveTimeoutId(newTimeoutId);
  }, [isUiExtensionOpen, isPinned, expanded, leaveTimeoutId]);

  // Toggle expand/collapse all
  const toggleExpandCollapseAll = useCallback(() => {
    const newExpandAllState = !expandAll;
    setExpandAll(newExpandAllState);

    setFileTree(prevTree => {
      const updateNodes = (nodes: FileNode[]): FileNode[] => {
        return nodes.map(n => ({
          ...n,
          isExpanded: n.type === 'folder' ? newExpandAllState : n.isExpanded,
          children: n.children ? updateNodes(n.children) : [],
        }));
      };
      return updateNodes(prevTree);
    });
  }, [expandAll]);

  // Centralized toggle node expansion for performance
  const handleToggleNodeExpansion = useCallback((nodeId: string) => {
    setFileTree(prevTree => {
      const findAndToggle = (nodes: FileNode[]): FileNode[] => {
        return nodes.map(n => {
          if (n.id === nodeId) {
            return { ...n, isExpanded: !n.isExpanded }; // Toggle based on current state in tree
          }
          if (n.children && n.children.length > 0) {
            return { ...n, children: findAndToggle(n.children) };
          }
          return n;
        });
      };
      return findAndToggle(prevTree);
    });
  }, []); // Depends only on setFileTree

  // Filter file tree based on search term
  const filteredFileTree = useMemo(() => {
    if (!searchTerm) return fileTree;

    const lowerSearchTerm = searchTerm.toLowerCase();

    const filterNodes = (nodes: FileNode[]): FileNode[] => {
      return nodes.reduce((acc, node) => {
        const nodeNameMatches = node.name.toLowerCase().includes(lowerSearchTerm);

        if (node.type === 'folder') {
          const filteredChildren = filterNodes(node.children);

          if (nodeNameMatches || filteredChildren.length > 0) {
            acc.push({
              ...node,
              children: filteredChildren,
              isExpanded: nodeNameMatches || filteredChildren.length > 0 ? true : node.isExpanded
            });
          }
        } else {
          if (nodeNameMatches) {
            acc.push(node);
          }
        }

        return acc;
      }, [] as FileNode[]);
    };

    return filterNodes(fileTree);
  }, [fileTree, searchTerm]);

  return {
    // State
    expanded,
    isPinned,
    fileTree,
    setFileTree,
    selectedTab,
    searchTerm,
    expandAll,
    isUiExtensionOpen,
    setIsUiExtensionOpen,
    sortOrder,
    bookmarkedFiles,
    setBookmarkedFiles,
    recentFiles,
    setRecentFiles,
    loading,
    setLoading,
    filteredFileTree,

    // Dialog states
    deleteDialogOpen,
    setDeleteDialogOpen,
    deleteTarget,
    setDeleteTarget,
    renameDialogOpen,
    setRenameDialogOpen,
    renameTarget,
    setRenameTarget,
    newNameInput,
    setNewNameInput,
    newFileDialogOpen,
    setNewFileDialogOpen,
    newFolderDialogOpen,
    setNewFolderDialogOpen,
    newItemParentPath,
    setNewItemParentPath,
    newItemName,
    setNewItemName,

    // Setters
    setSelectedTab,
    setSearchTerm,
    setSortOrder,

    // Handlers
    toggleExpanded,
    togglePin,
    handleMouseEnterSidebar,
    handleHoverStripEnter,
    handleSidebarLeave,
    toggleExpandCollapseAll,
    handleToggleNodeExpansion,
  };
};
