import React, { useState } from 'react';
import {
  ChevronLeft,
  Folder,
  FilePlus2,
  FolderPlus,
  RefreshCw,
  MoreVertical,
  Pin,
  PinOff,
  Check
} from 'lucide-react';
import { SidebarHeaderProps } from '../types';
import { cn } from '../utils';
import { Dropdown } from './Dropdown';

export const SidebarHeader = React.memo(({
  isPinned,
  onTogglePin,
  currentStoragePath,
  onSelectStorageFolder,
  onCreateFile,
  onCreateFolder,
  onRefresh,
  loading,
  sortOrder,
  setSortOrder,
  expandAll,
  toggleExpandCollapseAll,
  onToggleExpanded
}: SidebarHeaderProps) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);

  return (
    <div className="flex items-center justify-between p-2 border-b border-border sticky top-0 bg-background z-10 h-12">
      <div className="flex items-center gap-1">
        <button
          className="h-8 w-8 flex items-center justify-center rounded-md hover:bg-muted"
          onClick={onTogglePin}
          title={isPinned ? 'Unpin sidebar' : 'Pin sidebar'}
        >
          {isPinned ? <PinOff className="h-4 w-4" /> : <Pin className="h-4 w-4" />}
        </button>

        <button
          className="flex items-center gap-1 px-2 py-1 rounded-md hover:bg-muted"
          onClick={onSelectStorageFolder}
          title={currentStoragePath || "Select storage folder"}
        >
          <Folder className="h-4 w-4" />
          <span className="text-sm truncate max-w-[120px]">
            {currentStoragePath ? currentStoragePath.split('/').pop() : "Select Folder"}
          </span>
        </button>
      </div>

      <div className="flex items-center gap-1">
        {/* New file button */}
        <button
          className="h-8 w-8 flex items-center justify-center rounded-md hover:bg-muted"
          onClick={onCreateFile}
          title="New Document"
        >
          <FilePlus2 className="h-4 w-4" />
        </button>

        {/* New folder button */}
        <button
          className="h-8 w-8 flex items-center justify-center rounded-md hover:bg-muted"
          onClick={onCreateFolder}
          title="New Folder"
        >
          <FolderPlus className="h-4 w-4" />
        </button>

        {/* Refresh button */}
        <button
          className="h-8 w-8 flex items-center justify-center rounded-md hover:bg-muted"
          onClick={onRefresh}
          title="Refresh"
        >
          <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
        </button>

        {/* More options dropdown */}
        <Dropdown
          isOpen={dropdownOpen}
          setIsOpen={setDropdownOpen}
          trigger={
            <button
              className="h-8 w-8 flex items-center justify-center rounded-md hover:bg-muted"
              title="More Options"
            >
              <MoreVertical className="h-4 w-4" />
            </button>
          }
        >
          <button
            className="flex items-center gap-2 px-3 py-2 w-full text-left hover:bg-accent hover:text-accent-foreground"
            onClick={() => {
              setSortOrder('name');
              setDropdownOpen(false);
            }}
          >
            {sortOrder === 'name' && <Check className="h-4 w-4" />}
            <span className={sortOrder === 'name' ? "ml-1" : "ml-0"}>Sort by Name</span>
          </button>

          <button
            className="flex items-center gap-2 px-3 py-2 w-full text-left hover:bg-accent hover:text-accent-foreground"
            onClick={() => {
              setSortOrder('modified');
              setDropdownOpen(false);
            }}
          >
            {sortOrder === 'modified' && <Check className="h-4 w-4" />}
            <span className={sortOrder === 'modified' ? "ml-6" : "ml-0"}>Sort by Date</span>
          </button>

          <button
            className="flex items-center gap-2 px-3 py-2 w-full text-left hover:bg-accent hover:text-accent-foreground"
            onClick={() => {
              toggleExpandCollapseAll();
              setDropdownOpen(false);
            }}
          >
            {expandAll ? 'Collapse All' : 'Expand All'}
          </button>
        </Dropdown>
      </div>
    </div>
  );
});

SidebarHeader.displayName = 'SidebarHeader';
