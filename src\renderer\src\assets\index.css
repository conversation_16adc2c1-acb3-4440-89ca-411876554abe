@tailwind base;
@tailwind components;
@tailwind utilities;



/* Paragraph Vertical Gap */
.bn-block {
  margin-bottom: var(--paragraph-vertical-gap);
}

@layer base {
  /* Common variable setup for all themes */
  :root {
    --paragraph-vertical-gap: 1rem; /* Default paragraph gap */
    --radius: 0.5rem;
  }

  /* Light Theme - Ayu Light */
  .light {
    /* Base Colors */
    --background: 0 0% 99%; /* #fcfcfc */
    --foreground: 210 5% 38%; /* #5c6166 */

    /* New dedicated text color */
    --text-foreground: 210 5% 15%;
    --text-foreground-muted: 212 4% 35%;

    /* Card */
    --card: 0 0% 100%; /* #ffffff */
    --card-foreground: 210 5% 38%; /* #5c6166 */

    /* Popover */
    --popover: 0 0% 100%; /* #ffffff */
    --popover-foreground: 210 5% 38%; /* #5c6166 */

    /* UI Elements */
    --primary: 30 100% 60%; /* #ffaa33 */
    --primary-foreground: 0 0% 99%; /* #fcfcfc */
    --secondary: 0 0% 97%; /* #f8f8f8 */
    --secondary-foreground: 210 5% 38%; /* #5c6166 */
    --muted: 210 5% 96%; /* #f5f5f6 */
    --muted-foreground: 212 4% 45%; /* #787b80 */
    --muted-foreground-hover: 212 4% 35%; /* Darker version for hover */
    --accent: 30 100% 60%; /* #ffaa33 */
    --accent-foreground: 210 5% 38%; /* #5c6166 */
    --destructive: 0 74% 72%; /* #f07171 */
    --destructive-foreground: 0 0% 99%; /* #fcfcfc */

    /* Borders & Focus */
    --border: 212 4% 90%; /* #e6e6e8 */
    --input: 212 4% 90%; /* #e6e6e8 */
    --ring: 30 100% 60%; /* #ffaa33 */

    /* Sidebar Specific */
    --sidebar-background: 0 0% 99%; /* #fcfcfc */
    --sidebar-foreground: 210 5% 38%; /* #5c6166 */
    --sidebar-border: 212 4% 90%; /* #e6e6e8 */
    --sidebar-muted: 210 5% 96%; /* #f5f5f6 */
  }

  /* Dark Theme - One Dark Pro */
  .dark {
    /* Base Colors */
    --background: 220 13% 18%; /* #282c34 */
    --foreground: 220 10% 70%; /* #abb2bf */

    /* New dedicated text color */
    --text-foreground: 0 0% 98%;
    --text-foreground-muted: 0 0% 88%;

    /* Card */
    --card: 220 13% 18%; /* #282c34 */
    --card-foreground: 220 10% 70%; /* #abb2bf */

    /* Popover */
    --popover: 220 11% 15%; /* #21252b */
    --popover-foreground: 220 10% 70%; /* #abb2bf */

    /* UI Elements */
    --primary: 219 96% 53%; /* #61afef */
    --primary-foreground: 0 0% 100%; /* #ffffff */
    --secondary: 220 13% 23%; /* #323842 */
    --secondary-foreground: 220 10% 82%; /* #d7dae0 */
    --muted: 220 13% 23%; /* #323842 */
    --muted-foreground: 220 10% 65%; /* #9da5b4 */
    --muted-foreground-hover: 220 10% 50%; /* Darker version for hover */
    --accent: 286 60% 70%; /* #d39ade  */
    --accent-foreground: 220 10% 82%; /* #d7dae0 */
    --destructive: 5 65% 54%; /* #e06c75 */
    --destructive-foreground: 0 0% 100%; /* #ffffff */

    /* Borders & Focus */
    --border: 220 13% 26%; /* #3e4452 */
    --input: 220 11% 15%; /* #21252b */
    --ring: 219 96% 53%; /* #61afef */

    /* Sidebar Specific */
    --sidebar-background: 220 11% 15%; /* #21252b */
    --sidebar-foreground: 220 10% 70%; /* #abb2bf */
    --sidebar-border: 220 13% 26%; /* #3e4452 */
    --sidebar-muted: 220 13% 23%; /* #323842 */
  }

  /* Custom Theme - Ayu Mirage */
  .custom {
    /* Base colors */
    --background: 222 20% 18%; /* #252B37 */
    --foreground: 37 11% 78%; /* #cccac2 */

    /* New dedicated text color */
    --text-foreground: 0 0% 98%;
    --text-foreground-muted: 0 0% 88%;

    /* Card */
    --card: 217 18% 20%; /* #282e3b */
    --card-foreground: 37 11% 78%; /* #cccac2 */

    /* Popover */
    --popover: 217 18% 20%; /* #282e3b */
    --popover-foreground: 37 11% 78%; /* #cccac2 */

    /* UI Elements */
    --primary: 40 100% 70%; /* #ffcc66 */
    --primary-foreground: 222 20% 18%; /* #242936 */
    --secondary: 219 19% 29%; /* #3d4352 */
    --secondary-foreground: 37 11% 78%; /* #cccac2 */
    --muted: 219 19% 24%; /* #333845 */
    --muted-foreground: 217 15% 68%; /* #b8cfe6 */
    --muted-foreground-hover: 217 15% 53%; /* Darker version for hover */
    --accent: 187 50% 75%; /* #a9ebd4  */
    --accent-foreground: 222 20% 18%; /* #242936 */
    --destructive: 0 57% 73%; /* #f28779 */
    --destructive-foreground: 0 0% 100%; /* #ffffff */

    /* Improve heading contrast */
    --heading-color: 40 100% 80%; /* Brighter amber color for headings */

    /* Borders & Focus */
    --border: 219 19% 24%; /* #333845 */
    --input: 219 19% 24%; /* #333845 */
    --ring: 40 100% 70%; /* #ffcc66 */

    /* Sidebar Specific */
    --sidebar-background: 222 20% 18%; /* #242936 */
    --sidebar-foreground: 37 11% 78%; /* #cccac2 */
    --sidebar-border: 219 19% 24%; /* #333845 */
  }
}

/* BlockNote Theme Integration */
@layer base {
  /* Light theme - BlockNote backgrounds */
  .light .bn-editor {
    --bn-colors-editor-background: #fcfcfc;
  }
  .light .bn-container {
    --bn-colors-menu-background: #fcfcfc;
    --bn-colors-tooltip-background: #fcfcfc;
  }

  /* Dark theme - BlockNote backgrounds */
  .dark .bn-editor {
    --bn-colors-editor-background: #282C34;
  }
  .dark .bn-container {
    --bn-colors-menu-background: #282C34;
    --bn-colors-tooltip-background: #282C34;
  }

  /* Custom theme (Ayu Mirage) - BlockNote backgrounds */
  .custom .bn-editor {
    --bn-colors-editor-background: #252B37;
  }
  .custom .bn-container {
    --bn-colors-menu-background: #252B37;
    --bn-colors-tooltip-background: #252B37;
  }

  /* BlockNote Link Styling */
  /* Light theme links */
  .light .bn-editor a {
    color: hsl(var(--primary));
    text-decoration: underline;
    text-underline-offset: 2px;
    transition: color 0.2s ease-in-out;
  }

  .light .bn-editor a:hover {
    color: hsl(var(--primary) / 0.8);
    text-decoration: underline;
  }

  /* Dark theme links */
  .dark .bn-editor a {
    color: hsl(var(--accent));
    text-decoration: underline;
    text-underline-offset: 2px;
    transition: color 0.2s ease-in-out;
  }

  .dark .bn-editor a:hover {
    color: hsl(var(--accent) / 0.8);
    text-decoration: underline;
  }

  /* Custom theme links */
  .custom .bn-editor a {
    color: hsl(var(--accent));
    text-decoration: underline;
    text-underline-offset: 2px;
    transition: color 0.2s ease-in-out;
  }

  .custom .bn-editor a:hover {
    color: hsl(var(--accent) / 0.8);
    text-decoration: underline;
  }
}

/* BlockNote Static FormattingToolbar Layout */
@layer components {
  .bn-container {
    display: flex;
    flex-direction: column-reverse;
    gap: 8px;
  }

  .bn-formatting-toolbar {
    margin-inline: auto;
  }
}

/* Custom utility classes for app elements */
@layer components {
  .settings-sidebar-container {
    @apply bg-card border-r border-border flex flex-col h-full transition-all duration-300 ease-in-out;
  }

  .settings-sidebar-expanded {
    @apply w-64;
  }

  .settings-sidebar-header {
    @apply flex items-center justify-between p-3 border-b border-border;
  }

  .settings-sidebar-title {
    @apply text-lg font-semibold text-foreground;
  }

  .settings-sidebar-button {
    @apply p-2 rounded-md hover:bg-muted;
  }

  .hover-sidebar-strip {
    @apply fixed top-0 right-0 h-full w-2 bg-transparent z-50;
  }

  .main-content {
    @apply flex-1 p-0 overflow-auto bg-background;
  }

  .custom-switch-on {
    @apply bg-primary/80;
  }

  /* Switch styles are now handled directly in the Switch component */

  /* Floating UI positioning adjustment */
  [data-floating-ui-focusable] {
    left: -10px !important;
  }

  /* BlockNote Floating Toolbar Tooltip Positioning - Now handled by BlockNote's native API */
}

@layer utilities {
  .text-text-foreground {
    @apply text-[hsl(var(--text-foreground))];
  }

  /* Custom Scrollbar Utilities */
  /* Ensures .custom-scrollbar, .prompt-content-scrollbar (for textareas), and BlockNote editor have the same style */
  .custom-scrollbar::-webkit-scrollbar,
  .prompt-content-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track,
  .prompt-content-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb,
  .prompt-content-scrollbar::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground)); /* Changed from hsl(var(--ring)) */
    border-radius: 10px;
    transition: background-color 0.2s ease-in-out;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover,
  .prompt-content-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground-hover)); /* Changed from --primary */
  }
  /* BlockNote Editor Scrollbar Styling */
    main::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  main::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground));
    border-radius: 8px;
  }

  main::-webkit-scrollbar-track {
    background: transparent;
  }

  /* Ensure arrow icons are always visible on hover */
  .modal-nav-button:hover .arrow-icon {
    color: hsl(var(--foreground)) !important;
  }

  /* Dark and custom theme specific arrow visibility */
  .dark .modal-nav-button:hover .arrow-icon,
  .custom .modal-nav-button:hover .arrow-icon {
    color: hsl(var(--foreground)) !important;
    opacity: 1 !important;
  }

  /* Special notification styling for update workflow */
  .update-workflow-notification {
    border: 2px solid hsl(var(--primary)) !important;
    box-shadow: 0 4px 12px hsl(var(--primary) / 0.3) !important;
    animation: pulse-border 2s ease-in-out infinite !important;
  }

  @keyframes pulse-border {
    0%, 100% {
      border-color: hsl(var(--primary));
      box-shadow: 0 4px 12px hsl(var(--primary) / 0.3);
    }
    50% {
      border-color: hsl(var(--primary) / 0.7);
      box-shadow: 0 6px 16px hsl(var(--primary) / 0.5);
    }
  }

}
