{"name": "trufflenote", "version": "1.0.0", "description": "A notepad geared for LLMs", "main": "./out/main/index.js", "author": "Pantheon Network <<EMAIL>>", "homepage": "https://pantheonnetwork.co", "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:win": "npm run build && electron-builder --win --config", "build:mac": "electron-vite build && electron-builder --mac --config", "build:linux": "electron-vite build && electron-builder --linux --config", "build:win:publish": "npm run build && electron-builder --win --config --publish=always", "build:mac:publish": "electron-vite build && electron-builder --mac --config --publish=always", "build:linux:publish": "electron-vite build && electron-builder --linux --config --publish=always", "build:all:publish": "npm run build && electron-builder --win --mac --linux --config --publish=always"}, "dependencies": {"@blocknote/core": "^0.31.0", "@blocknote/mantine": "^0.31.0", "@blocknote/react": "^0.31.0", "@electron-toolkit/preload": "^3.0.0", "@electron-toolkit/utils": "^3.0.0", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.6", "@redux-devtools/extension": "^3.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "electron-store": "^8.2.0", "electron-updater": "^6.1.7", "framer-motion": "^12.12.1", "fs-extra": "^11.2.0", "immer": "^10.1.1", "lucide-react": "^0.314.0", "nanoid": "^3.3.7", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "remove-markdown": "^0.6.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zustand": "^4.5.0"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^2.0.0", "@electron-toolkit/eslint-config-ts": "^1.0.1", "@electron-toolkit/tsconfig": "^1.0.1", "@types/fs-extra": "^11.0.4", "@types/node": "^18.19.5", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@types/remove-markdown": "^0.3.4", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "electron": "^28.1.1", "electron-builder": "^24.9.1", "electron-vite": "^2.0.0", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "postcss": "^8.4.33", "prettier": "^3.1.1", "react": "^18.2.0", "react-dom": "^18.2.0", "shadcn-ui": "^0.8.0", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^5.0.11", "vite-tsconfig-paths": "^5.1.4"}}