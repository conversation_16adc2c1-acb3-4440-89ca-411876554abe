import path, { resolve } from 'path';
import { fileURLToPath } from 'url';
import { defineConfig, externalizeDepsPlugin } from 'electron-vite';
import react from '@vitejs/plugin-react';
import tsconfigPaths from 'vite-tsconfig-paths'; // Ensure this is imported

const __filenameESM = fileURLToPath(import.meta.url);
const __dirnameESM = path.dirname(__filenameESM);

// Optional: You can keep or remove your manual alias definition and logging
// For now, let's rely on vite-tsconfig-paths to do its job.
// const rendererAliasesConfig = { /* ... */ };

export default defineConfig({
  main: {
    plugins: [externalizeDepsPlugin()]
  },
  preload: {
    plugins: [externalizeDepsPlugin({
      exclude: ['@electron-toolkit/preload']
    })],
    build: {
      rollupOptions: {
        external: []
      }
    }
  },
  renderer: {
    // If you want to rely solely on vite-tsconfig-paths for the aliases defined in tsconfig.web.json,
    // you can comment out the manual `resolve.alias` section.
    // resolve: {
    //   alias: rendererAliasesConfig
    // },
    plugins: [
      react(),
      tsconfigPaths({
        // Correct the path to your tsconfig.web.json
        // It should be relative to the electron.vite.config.ts file,
        // or an absolute path. Since __dirnameESM is project root:
        projects: [resolve(__dirnameESM, 'tsconfig.web.json')]
      }),
      // Keep your logging plugin to verify
      {
        name: 'vite-log-final-config',
        configResolved(resolvedConfig) {
          console.log("\n[Vite Internal LOG] --- Vite's Actual Resolved Aliases (Renderer) ---");
          const finalAliases = resolvedConfig.resolve.alias;
          if (Array.isArray(finalAliases)) {
            finalAliases.forEach(a => {
              if (typeof a.find === 'string' && (a.find.startsWith('@'))) {
                console.log(`[Vite Internal LOG] Find: ${String(a.find)}, Replacement: ${a.replacement}`);
              }
            });
          }
          console.log("[Vite Internal LOG] --------------------------------------------- \n");
        }
      }
    ]
  }
});
