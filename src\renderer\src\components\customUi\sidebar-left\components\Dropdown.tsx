import React, { useRef, useEffect } from 'react';
import { DropdownProps } from '../types';

// Dropdown menu component - Extracted and memoized
export const Dropdown = React.memo(({
  trigger,
  children,
  isOpen,
  setIsOpen
}: DropdownProps) => {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [setIsOpen]);

  return (
    <div className="relative" ref={ref}>
      <div onClick={() => setIsOpen(!isOpen)}>
        {trigger}
      </div>
      {isOpen && (
        <div className="absolute right-0 mt-1 w-48 bg-popover text-popover-foreground rounded-md shadow-lg z-10 border border-border">
          {children}
        </div>
      )}
    </div>
  );
});

Dropdown.displayName = 'Dropdown';
