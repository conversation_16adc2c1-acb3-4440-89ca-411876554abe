// This is the updated sidebar-right.tsx that uses modular components
// Your application can continue using this file exactly as before

// Export the context and provider
export { SettingsSidebarProvider, useSettingsSidebar } from './SettingsSidebarContext';

// Export the main components
export { SettingsSidebar } from './SettingsSidebarMain';
export { SettingsSidebarButton } from './SettingsSidebarButton';

// Export types for external use if needed
export type {
  SettingsSidebarContextType,
  SettingsSidebarProps
} from './SettingsSidebarTypes';

// Re-export individual components if you want to use them separately
export { SettingsSidebarHeader } from './SettingsSidebarHeader';
export { SettingsSidebarContent } from './SettingsSidebarContent';
export { SettingsSidebarDocumentSettings } from './SettingsSidebarDocumentSettings';
export { SettingsSidebarPrompts } from './SettingsSidebarPrompts';
