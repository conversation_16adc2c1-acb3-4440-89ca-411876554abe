// Types and interfaces for the Settings Sidebar
import { ProjectSettings, DocumentSettings, FrontendStoredFile } from '@/types/global';

export interface SettingsSidebarContextType {
  expanded: boolean;
  toggleExpanded: () => void;
  isPinned: boolean;
  togglePinned: () => void;
  setIsUiExtensionOpen: (isOpen: boolean) => void;
  isUiExtensionOpen: boolean;
  isApiKeysModalOpen: boolean;
  setIsApiKeysModalOpen: (isOpen: boolean) => void;
  isManagePromptsModalOpen: boolean;
  setIsManagePromptsModalOpen: (isOpen: boolean) => void;
}

export interface SettingsSidebarProps {
  projectSettings?: ProjectSettings | null;
  activeFile?: FrontendStoredFile | null;
  onUpdateProjectSettings?: (updatedSettings: ProjectSettings) => Promise<void>;
  onSaveActiveDocumentSettings?: (updatedDocSettings: DocumentSettings) => Promise<void>;
}

export interface SettingsSidebarHeaderProps {
  isPinned: boolean;
  togglePinned: () => void;
  toggleExpanded: () => void;
}

export interface SettingsSidebarContentProps {
  activeFile: FrontendStoredFile | null;
  projectSettings: ProjectSettings | null;
  documentSettings: DocumentSettings | null;
  onUpdateProjectSettings: (updatedSettings: ProjectSettings) => Promise<void>;
  onSaveActiveDocumentSettings: (updatedDocSettings: DocumentSettings) => Promise<void>;
  isUiExtensionOpen: boolean;
  setIsUiExtensionOpen: (isOpen: boolean) => void;
}

export interface PromptManagementSectionProps {
  activeFile: FrontendStoredFile;
  projectSettings: ProjectSettings;
  documentSettings: DocumentSettings;
  onUpdateProjectSettings: (updatedSettings: ProjectSettings) => Promise<void>;
  onSaveActiveDocumentSettings: (updatedDocSettings: DocumentSettings) => Promise<void>;
  // Note: UI extension state is now automatically managed by SettingsSidebarContext
}

export interface DocumentSettingsSectionProps {
  activeFile: FrontendStoredFile;
  projectSettings: ProjectSettings;
  documentSettings: DocumentSettings;
  onSaveActiveDocumentSettings: (updatedDocSettings: DocumentSettings) => Promise<void>;
  onUpdateProjectSettings: (updatedSettings: ProjectSettings) => Promise<void>;
  setIsUiExtensionOpen: (isOpen: boolean) => void;
}

export interface SavedParagraphsSectionProps {
  // No props needed - context automatically manages UI extension state
}
