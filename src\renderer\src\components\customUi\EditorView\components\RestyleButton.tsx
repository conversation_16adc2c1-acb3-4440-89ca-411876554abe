import React from 'react';
import { useComponentsContext } from '@blocknote/react';
import { Wand2 } from 'lucide-react';

/**
 * Custom Restyle Button Component for BlockNote Floating Toolbar
 * Uses BlockNote's proper API for consistent tooltip positioning
 */
export const RestyleButton: React.FC = () => {
  const Components = useComponentsContext()!;

  const handleRestyle = () => {
    console.log('Restyle clicked');
  };

  return (
    <Components.FormattingToolbar.Button
      mainTooltip="Restyle"
      onClick={handleRestyle}
    >
      <Wand2 size={16} />
    </Components.FormattingToolbar.Button>
  );
};
