import { Block, BlockNoteEditor } from '@blocknote/core';
import removeMarkdown from 'remove-markdown';
import { COPY_CONSTANTS } from './../constants';

// Types for copy functionality
interface SelectionState {
  browserSelection: Selection | null;
  rawSelectedText: string;
  hasActiveVisualSelection: boolean;
}

interface CopyContent {
  plainText: string;
  html: string;
}

/**
 * Analyzes the current browser selection state
 */
export const getSelectionState = (): SelectionState => {
  const browserSelection = window.getSelection();
  const rawSelectedText = browserSelection?.toString() || "";
  const hasActiveVisualSelection = browserSelection &&
    !browserSelection.isCollapsed &&
    rawSelectedText.length > 0;

  return {
    browserSelection,
    rawSelectedText,
    hasActiveVisualSelection
  };
};

/**
 * Safely generates HTML from browser selection using DOM cloning
 */
export const generateHtmlFromSelection = (selection: Selection, fallbackText: string): string => {
  try {
    const range = selection.getRangeAt(0);
    const documentFragment = range.cloneContents();
    const tempDiv = document.createElement('div');
    tempDiv.appendChild(documentFragment);

    // Sanitize the HTML by removing potentially dangerous elements
    const sanitizedHtml = tempDiv.innerHTML;

    // Fallback if innerHTML is empty but selected text wasn't
    if (!sanitizedHtml.trim() && fallbackText.trim()) {
      return COPY_CONSTANTS.FALLBACK_HTML(fallbackText);
    }

    return sanitizedHtml;
  } catch (error) {
    console.error("[EditorView] Error creating HTML from browser selection range:", error);
    return COPY_CONSTANTS.FALLBACK_HTML(fallbackText);
  }
};

/**
 * Generates content from BlockNote blocks
 */
export const generateContentFromBlocks = async (
  editor: BlockNoteEditor,
  blocks: Block[]
): Promise<CopyContent> => {
  const markdownContent = await editor.blocksToMarkdownLossy(blocks);
  const plainText = removeMarkdown(markdownContent);
  const html = await editor.blocksToHTMLLossy(blocks);

  return { plainText, html };
};

/**
 * Applies system prompt prepending to plain text
 */
export const applySystemPromptPrepend = (
  plainText: string,
  isPrependEnabled: boolean,
  systemPrompt: string
): string => {
  if (isPrependEnabled && systemPrompt.trim()) {
    const result = `${systemPrompt.trim()}\n\n${plainText}`;
    console.log('[EditorView] 🔧 applySystemPromptPrepend DEBUG:');
    console.log('  🎯 System prompt:', JSON.stringify(systemPrompt.trim()));
    console.log('  📝 Input text:', JSON.stringify(plainText));
    console.log('  📝 Result:', JSON.stringify(result));
    console.log('  🔢 Input newlines:', (plainText.match(/\n/g) || []).length);
    console.log('  🔢 Result newlines:', (result.match(/\n/g) || []).length);
    return result;
  }
  return plainText;
};

/**
 * Applies system prompt prepending to HTML content
 */
export const applySystemPromptPrependToHtml = (
  html: string,
  isPrependEnabled: boolean,
  systemPrompt: string
): string => {
  if (isPrependEnabled && systemPrompt.trim()) {
    // Create a simple paragraph for the system prompt
    const promptHtml = `<p>${systemPrompt.trim().replace(/\n/g, '<br>')}</p><p><br></p>`;
    return promptHtml + html;
  }
  return html;
};
