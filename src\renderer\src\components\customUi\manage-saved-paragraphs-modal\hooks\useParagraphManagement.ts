import { useCallback, useState, useEffect, useMemo } from 'react';
import { useSavedParagraphsStore } from '@/stores/savedParagraphsStore';
import { SavedParagraphMetadata } from '@/types/global';
import { useNotification } from '@/contexts/NotificationContext';

// Custom debounce hook for search optimization
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Hook for managing saved paragraphs operations
 * Provides convenient methods for CRUD operations on saved paragraphs
 */
export const useParagraphManagement = () => {
  const [fullTextResults, setFullTextResults] = useState<SavedParagraphMetadata[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  const {
    paragraphs,
    categories,
    isLoading,
    error,
    selectedParagraphId,
    selectedCategoryIds,
    searchQuery,
    currentView,
    initialCreateContent,

    // Actions
    initializeData,
    loadParagraphs,
    loadCategories,
    createParagraph,
    updateParagraph,
    deleteParagraph,
    setSelectedParagraph,
    setSelectedCategory,
    setSearchQuery,
    setCurrentView,
    setInitialCreateContent,
    clearError,

    // Helper methods
    getFilteredParagraphs,
    generateContentPreview
  } = useSavedParagraphsStore();

  const { showNotification } = useNotification();

  // Debounce search query for full-text search
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Memoized synchronous filtering for immediate UI updates
  const displayedParagraphs = useMemo(() => {
    let filtered = paragraphs;

    // Filter by selected categories
    if (selectedCategoryIds.length > 0) {
      const specialNoCategory = '__no_category__';
      const hasNoCategoryFilter = selectedCategoryIds.includes(specialNoCategory);

      filtered = filtered.filter(p => {
        // Handle special case for paragraphs with no category
        if (hasNoCategoryFilter && (!p.categoryId || p.categoryId === '')) {
          return true;
        }

        // Handle normal category filtering
        if (selectedCategoryIds.includes(p.categoryId)) {
          return true;
        }

        return false;
      });
    }

    // Quick search by title, description, and tags
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(p =>
        p.title.toLowerCase().includes(query) ||
        p.description?.toLowerCase().includes(query) ||
        p.contentPreview.toLowerCase().includes(query) ||
        p.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }

    return [...filtered].sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
  }, [paragraphs, selectedCategoryIds, searchQuery]);

  // Debounced full-text search effect
  useEffect(() => {
    if (!debouncedSearchQuery.trim()) {
      setFullTextResults([]);
      setIsSearching(false);
      return;
    }

    // Only perform full-text search if quick search returned no results
    if (displayedParagraphs.length > 0) {
      setFullTextResults([]);
      setIsSearching(false);
      return;
    }

    let cancelled = false;
    setIsSearching(true);

    const performFullTextSearch = async () => {
      try {
        const results = await getFilteredParagraphs();
        if (!cancelled) {
          setFullTextResults(results);
          setIsSearching(false);
        }
      } catch (error) {
        console.error('Full-text search error:', error);
        if (!cancelled) {
          setFullTextResults([]);
          setIsSearching(false);
        }
      }
    };

    performFullTextSearch();

    return () => {
      cancelled = true;
    };
  }, [debouncedSearchQuery, displayedParagraphs.length, getFilteredParagraphs]);

  // Use full-text results when available and quick search returned no results
  const finalDisplayedParagraphs = fullTextResults.length > 0 ? fullTextResults : displayedParagraphs;

  // Initialize data on first load - now uses the store's initializeData method
  const initializeDataSafe = useCallback(async () => {
    try {
      await initializeData();
    } catch (error) {
      console.error('Failed to initialize saved paragraphs data:', error);
      showNotification('Failed to load saved paragraphs', 'error');
    }
  }, [initializeData, showNotification]);

  // Create a new paragraph with validation
  const createParagraphSafe = useCallback(async (
    title: string,
    categoryId: string,
    content: any,
    description?: string,
    tags?: string[]
  ) => {
    console.log('🚀 [useParagraphManagement] createParagraphSafe called with:', { title, categoryId, content, description, tags });
    if (!title.trim()) {
      showNotification('Paragraph title is required', 'error');
      return false;
    }

    if (!content || (Array.isArray(content) && content.length === 0)) {
      showNotification('Paragraph content cannot be empty', 'error');
      return false;
    }

    try {
      console.log('🔄 [useParagraphManagement] createParagraphSafe: Attempting to create paragraph via store');
      await createParagraph(title.trim(), categoryId, content, description?.trim(), tags);
      showNotification(`Paragraph "${title}" saved successfully`, 'success');
      console.log('✅ [useParagraphManagement] createParagraphSafe: Paragraph created successfully');
      return true;
    } catch (error) {
      console.error('Failed to create paragraph:', error);
      showNotification('Failed to save paragraph', 'error');
      return false;
    }
  }, [createParagraph, showNotification]);

  // Update a paragraph with validation
  const updateParagraphSafe = useCallback(async (
    id: string,
    updates: Partial<SavedParagraphMetadata>,
    content?: any
  ) => {
    console.log('🚀 [useParagraphManagement] updateParagraphSafe called with:', { id, updates, content });
    if (updates.title && !updates.title.trim()) {
      showNotification('Paragraph title cannot be empty', 'error');
      return false;
    }

    try {
      console.log('🔄 [useParagraphManagement] updateParagraphSafe: Attempting to update paragraph via store');
      await updateParagraph(id, updates, content);
      showNotification('Paragraph updated successfully', 'success');
      console.log('✅ [useParagraphManagement] updateParagraphSafe: Paragraph updated successfully');
      return true;
    } catch (error) {
      console.error('Failed to update paragraph:', error);
      showNotification('Failed to update paragraph', 'error');
      return false;
    }
  }, [updateParagraph, showNotification]);

  // Delete a paragraph with confirmation
  const deleteParagraphSafe = useCallback(async (id: string, title: string) => {
    console.log('🚀 [useParagraphManagement] deleteParagraphSafe called with:', { id, title });
    try {
      console.log('🔄 [useParagraphManagement] deleteParagraphSafe: Attempting to delete paragraph via store');
      await deleteParagraph(id);
      showNotification(`Paragraph "${title}" deleted successfully`, 'success');
      console.log('✅ [useParagraphManagement] deleteParagraphSafe: Paragraph deleted successfully');

      // Clear selection if the deleted paragraph was selected
      if (selectedParagraphId === id) {
        setSelectedParagraph(null);
      }

      return true;
    } catch (error) {
      console.error('Failed to delete paragraph:', error);
      showNotification('Failed to delete paragraph', 'error');
      return false;
    }
  }, [deleteParagraph, selectedParagraphId, setSelectedParagraph, showNotification]);

  // Get paragraph by ID
  const getParagraphById = useCallback((id: string) => {
    return paragraphs.find(p => p.id === id) || null;
  }, [paragraphs]);

  // Get category by ID
  const getCategoryById = useCallback((id: string) => {
    return categories.find(c => c.id === id) || null;
  }, [categories]);

  // Navigation helpers
  const navigateToList = useCallback(() => {
    setCurrentView('list');
    setSelectedParagraph(null);
  }, [setCurrentView, setSelectedParagraph]);

  const navigateToCreate = useCallback((initialContent?: any[] | null) => {
    setCurrentView('create');
    setSelectedParagraph(null);
    if (initialContent) {
      setInitialCreateContent(initialContent);
    }
  }, [setCurrentView, setSelectedParagraph, setInitialCreateContent]);

  const navigateToEdit = useCallback((paragraphId: string) => {
    setSelectedParagraph(paragraphId);
    setCurrentView('edit');
  }, [setSelectedParagraph, setCurrentView]);

  const navigateToCategoryManagement = useCallback(() => {
    setCurrentView('category-management');
    setSelectedParagraph(null);
  }, [setCurrentView, setSelectedParagraph]);

  return {
    // State
    paragraphs,
    categories,
    isLoading: isLoading || isSearching,
    error,
    selectedParagraphId,
    selectedCategoryIds,
    searchQuery,
    currentView,
    initialCreateContent,

    // Computed data
    displayedParagraphs: finalDisplayedParagraphs,
    selectedParagraph: selectedParagraphId ? getParagraphById(selectedParagraphId) : null,
    selectedCategories: selectedCategoryIds.map(id => getCategoryById(id)).filter(Boolean),

    // Actions
    initializeData: initializeDataSafe,
    createParagraph: createParagraphSafe,
    updateParagraph: updateParagraphSafe,
    deleteParagraph: deleteParagraphSafe,

    // Getters
    getParagraphById,
    getCategoryById,
    generateContentPreview,

    // Filters and search
    setSelectedCategory,
    setSearchQuery,
    clearError,

    // Navigation
    navigateToList,
    navigateToCreate,
    navigateToEdit,
    navigateToCategoryManagement,

    // Raw store methods (for advanced usage)
    rawStore: {
      setCurrentView,
      setSelectedParagraph,
      loadParagraphs,
      loadCategories
    }
  };
};
