// Types for the sidebar components
export interface FileNode {
  id: string;
  name: string;
  type: 'file' | 'folder';
  children: FileNode[];
  isExpanded?: boolean;
  parentId?: string | null;
  updatedAt?: string;
}

export interface SidebarProps {
  onSelectStorageFolder?: () => void;
}

export type SelectedTab = 'files' | 'recent' | 'bookmarks';
export type SortOrder = 'name' | 'modified';

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  footer: React.ReactNode;
}

export interface DropdownProps {
  trigger: React.ReactNode;
  children: React.ReactNode;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

export interface NodeItemProps {
  node: FileNode;
  level: number;
  currentDocId?: string | null;
  bookmarkedFiles: FileNode[];
  onFileOpen: (fileId: string) => void;
  onToggleExpansion: (nodeId: string) => void;
  onCreateFile: (parentId: string) => void;
  onCreateFolder: (parentId: string) => void;
  onRename: (node: FileNode) => void;
  onDelete: (node: FileNode) => void;
  onToggleBookmark: (node: FileNode) => void;
  onItemMove: (itemId: string, newParentId: string | null) => Promise<void>;
}

export interface SidebarHeaderProps {
  isPinned: boolean;
  onTogglePin: () => void;
  currentStoragePath: string | null;
  onSelectStorageFolder: () => void;
  onCreateFile: () => void;
  onCreateFolder: () => void;
  onRefresh: () => void;
  loading: boolean;
  sortOrder: SortOrder;
  setSortOrder: (order: SortOrder) => void;
  expandAll: boolean;
  toggleExpandCollapseAll: () => void;
  onToggleExpanded: () => void;
}

export interface SearchInputProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}

export interface TabButtonsProps {
  selectedTab: SelectedTab;
  setSelectedTab: (tab: SelectedTab) => void;
}

export interface SidebarContentProps {
  selectedTab: SelectedTab;
  currentStoragePath: string | null;
  loading: boolean;
  filteredFileTree: FileNode[];
  recentFiles: FileNode[];
  bookmarkedFiles: FileNode[];
  searchTerm: string;
  currentDocId?: string | null;
  onFileOpen: (fileId: string) => void;
  onToggleExpansion: (nodeId: string) => void;
  onCreateFile: (parentId?: string) => void;
  onCreateFolder: (parentId?: string) => void;
  onRename: (node: FileNode) => void;
  onDelete: (node: FileNode) => void;
  onToggleBookmark: (node: FileNode) => void;
  onItemMove: (itemId: string, newParentId: string | null) => Promise<void>;
}

export interface RootDropAreaProps {
  onItemMove: (itemId: string, newParentId: string | null) => Promise<void>;
}
