import * as React from "react"
import * as SwitchPrimitives from "@radix-ui/react-switch"
import { cn } from "@/lib/utils"

const Switch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>
>(({ className, ...props }, ref) => (
  <SwitchPrimitives.Root
    ref={ref}
    {...props}
    className={cn(
      // Track styles
      "peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full transition-colors",
      "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
      "disabled:cursor-not-allowed disabled:opacity-50",
      // Make track darker than background
      "bg-border data-[state=checked]:bg-border/70", // visible in both themes
      className
    )}
  >
    <SwitchPrimitives.Thumb
      className={cn(
        // Base thumb size and transition
        "pointer-events-none block h-5 w-5 rounded-full shadow-md ring-0 transition-transform",
        // Thumb position
        "data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0",
        // Thumb color
        "bg-muted-foreground data-[state=checked]:bg-primary"
      )}
    />
  </SwitchPrimitives.Root>
))

Switch.displayName = SwitchPrimitives.Root.displayName
export { Switch }
