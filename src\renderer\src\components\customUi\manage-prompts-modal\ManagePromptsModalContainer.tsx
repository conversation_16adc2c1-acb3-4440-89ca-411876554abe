import React, { useCallback, useEffect, memo } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '@/ui/dialog';
import { Button } from '@/ui/button';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/ui/tabs';
import { useDocumentStore, useActiveDocument, useProjectSettings } from '@/stores/blockDocumentStore';

// Components
import { GlobalPromptsTab } from './components/GlobalPromptsTab';
import { DocumentPromptsTab } from './components/DocumentPromptsTab';
import { ExpandedEditor } from './components/ExpandedEditor';
import { DeleteConfirmDialog } from './components/DeleteConfirmDialog';

// Hooks
import { usePromptManagement } from './hooks/usePromptManagement';
import { usePromptActions } from './hooks/usePromptActions';
import { usePromptOperations } from './hooks/usePromptOperations';

// Types
import { ManagePromptsModalProps } from './types';

const ManagePromptsModalComponent: React.FC<ManagePromptsModalProps> = ({
  isOpen,
  onClose
}) => {
  console.log('[ManagePromptsModalComponent Mount/Re-render] isOpen:', isOpen);

  // Use selectors for optimized renders
  const projectSettings = useProjectSettings();
  const activeFile = useActiveDocument();
  const { saveProjectSettings, saveDocumentMetadata } = useDocumentStore();

  // Derived values
  const activeDocumentId = activeFile?.id || null;
  const activeDocumentSettings = activeFile?.documentSettings || null;

  // Define the handlers using the store methods with React.useCallback for memoization
  const onUpdateProjectSettings = React.useCallback(async (updatedSettings: any) => {
    console.log('[ManagePromptsModalComponent onUpdateProjectSettings] updatedSettings:', updatedSettings);
    try {
      await saveProjectSettings(updatedSettings);
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  }, [saveProjectSettings]);

  const onUpdateActiveDocumentSettings = React.useCallback(async (updatedSettings: any) => {
    console.log('[ManagePromptsModalComponent onUpdateActiveDocumentSettings] updatedSettings:', updatedSettings);
    if (!activeDocumentId) return Promise.reject(new Error('No active document'));
    try {
      await saveDocumentMetadata(activeDocumentId, updatedSettings);
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  }, [activeDocumentId, saveDocumentMetadata]);

  // Main prompt management hook
  const {
    activeTab,
    setActiveTab,
    editedGlobalPrompts,
    setEditedGlobalPrompts,
    editedLocalPrompts,
    setEditedLocalPrompts,
    newGlobalPromptName,
    setNewGlobalPromptName,
    newLocalPromptName,
    setNewLocalPromptName,
    promptToDelete,
    setPromptToDelete,
    isDeleteConfirmOpen,
    setIsDeleteConfirmOpen,
    expandedEditorOpen,
    setExpandedEditorOpen,
    promptToEdit,
    setPromptToEdit,
    handleUpdatePromptContent,
    handleUpdatePromptName,
    handleCreateNewGlobalPrompt,
    handleCreateNewLocalPrompt,
    saveAllPendingChanges,
    saveAllPendingChangesRef
  } = usePromptManagement({
    isOpen,
    projectSettings,
    activeDocumentSettings,
    activeDocumentId,
    onUpdateProjectSettings,
    onUpdateActiveDocumentSettings
  });

  // Prompt actions hook
  const {
    handleForkGlobalToLocal,
    handleSaveLocalAsGlobal,
    handleReplaceGlobal,
    handleResetToGlobal,
    handleDetachFromGlobal,
    handleConvertToGlobal
  } = usePromptActions({
    editedGlobalPrompts,
    setEditedGlobalPrompts,
    editedLocalPrompts,
    setEditedLocalPrompts,
    projectSettings,
    activeDocumentSettings,
    activeDocumentId,
    onUpdateProjectSettings,
    onUpdateActiveDocumentSettings,
    setActiveTab
  });

  // Prompt operations hook
  const {
    handleDeletePrompt,
    handleVisibilityToggle,
    moveGlobalPrompt,
    moveLocalPrompt
  } = usePromptOperations({
    editedGlobalPrompts,
    setEditedGlobalPrompts,
    editedLocalPrompts,
    setEditedLocalPrompts,
    projectSettings,
    activeDocumentSettings,
    activeDocumentId,
    onUpdateProjectSettings,
    onUpdateActiveDocumentSettings
  });

  // Set up auto-save before unmounting
  useEffect(() => {
    console.log(
      '[ManagePromptsModalComponent useEffect - saveAllPendingChangesRef setup] isOpen:',
      isOpen,
      'saveAllPendingChangesRef.current:',
      !!saveAllPendingChangesRef.current
    );

    if (isOpen && saveAllPendingChangesRef.current) {
      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === 'Escape' && saveAllPendingChangesRef.current) {
          saveAllPendingChangesRef.current();
        }
      };

      window.addEventListener('keydown', handleEscape);
      return () => window.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, saveAllPendingChangesRef]);

  const handleClose = useCallback(() => {
    console.log('[ManagePromptsModalComponent handleClose] CALLED.');
    saveAllPendingChanges();
    onClose();
  }, [saveAllPendingChanges, onClose]);

  const handleInitiateDelete = useCallback((prompt: any) => {
    setPromptToDelete(prompt);
    setIsDeleteConfirmOpen(true);
  }, [setPromptToDelete, setIsDeleteConfirmOpen]);

  const handleConfirmDelete = useCallback((event?: React.MouseEvent) => {
    event?.stopPropagation(); // Prevent event from bubbling up

    if (!promptToDelete) {
      return;
    }

    handleDeletePrompt(promptToDelete);
    setPromptToDelete(null);
    setIsDeleteConfirmOpen(false);
  }, [promptToDelete, handleDeletePrompt, setPromptToDelete, setIsDeleteConfirmOpen]);

  if (!isOpen) {
    console.log('[ManagePromptsModalComponent] isOpen is false, returning null.');
    return null;
  }

  console.log('[ManagePromptsModalComponent Render] Bottom of component. isOpen:', isOpen, 'activeTab:', activeTab);

  return (
    <DndProvider backend={HTML5Backend}>
      <Dialog open={isOpen} onOpenChange={(open) => { if (!open) handleClose(); }}>
        <DialogContent className="max-w-[90vw] h-[90vh] flex flex-col p-0 gap-0 overflow-hidden">
          <DialogHeader className="p-4">
            <DialogTitle className="text-foreground text-sm">Manage Prompts</DialogTitle>
            <DialogDescription>
              Organize and manage your global and document-specific system prompts.
            </DialogDescription>
          </DialogHeader>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-grow flex flex-col overflow-hidden">
            <TabsList className="grid w-full grid-cols-2 border-b">
              <TabsTrigger
                value="global"
                className="text-center px-4 pb-3 text-sm font-medium rounded-none border-b-2 data-[state=active]:text-primary data-[state=active]:border-primary text-muted-foreground border-transparent hover:text-foreground data-[state=inactive]:hover:border-border"
              >
                Global Prompts ({editedGlobalPrompts.length})
              </TabsTrigger>
              {activeDocumentId && (
                <TabsTrigger
                  value="document"
                  className="text-center px-4 pb-3 text-sm font-medium rounded-none border-b-2 data-[state=active]:text-primary data-[state=active]:border-primary text-muted-foreground border-transparent hover:text-foreground data-[state=inactive]:hover:border-border"
                >
                  Document Prompts ({editedLocalPrompts.length})
                </TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="global" className="flex-grow overflow-y-auto p-4 m-0">
              <GlobalPromptsTab
                prompts={editedGlobalPrompts}
                newPromptName={newGlobalPromptName}
                setNewPromptName={setNewGlobalPromptName}
                onCreatePrompt={handleCreateNewGlobalPrompt}
                movePrompt={moveGlobalPrompt}
                activeDocumentId={activeDocumentId}
                activeDocumentSettings={activeDocumentSettings}
                handleVisibilityToggle={handleVisibilityToggle}
                handleInitiateDelete={handleInitiateDelete}
                handleUpdatePromptContent={handleUpdatePromptContent}
                handleUpdatePromptName={handleUpdatePromptName}
                projectSettingsSystemPrompts={projectSettings?.systemPrompts || []}
                setPromptToEdit={setPromptToEdit}
                setExpandedEditorOpen={setExpandedEditorOpen}
                promptActions={{
                  onForkToLocal: handleForkGlobalToLocal
                }}
              />
            </TabsContent>

            {activeDocumentId && (
              <TabsContent value="document" className="flex-grow overflow-y-auto p-4 m-0">
                <DocumentPromptsTab
                  prompts={editedLocalPrompts}
                  newPromptName={newLocalPromptName}
                  setNewPromptName={setNewLocalPromptName}
                  onCreatePrompt={handleCreateNewLocalPrompt}
                  movePrompt={moveLocalPrompt}
                  activeDocumentId={activeDocumentId}
                  activeDocumentSettings={activeDocumentSettings}
                  handleVisibilityToggle={handleVisibilityToggle}
                  handleInitiateDelete={handleInitiateDelete}
                  handleUpdatePromptContent={handleUpdatePromptContent}
                  handleUpdatePromptName={handleUpdatePromptName}
                  projectSettingsSystemPrompts={projectSettings?.systemPrompts || []}
                  setPromptToEdit={setPromptToEdit}
                  setExpandedEditorOpen={setExpandedEditorOpen}
                  globalPrompts={editedGlobalPrompts}
                  promptActions={{
                    onSaveAsGlobal: handleSaveLocalAsGlobal,
                    onReplaceGlobal: handleReplaceGlobal,
                    onResetToGlobal: handleResetToGlobal,
                    onDetachFromGlobal: handleDetachFromGlobal,
                    onConvertToGlobal: handleConvertToGlobal
                  }}
                />
              </TabsContent>
            )}
          </Tabs>

          <DialogFooter className="p-4 flex justify-between items-center">
            <p className="text-sm text-muted-foreground italic">All changes are automagically saved.</p>
            <Button variant="outline" onClick={handleClose}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Expanded Editor Dialog */}
      <ExpandedEditor
        isOpen={expandedEditorOpen}
        onClose={() => setExpandedEditorOpen(false)}
        prompt={promptToEdit}
        onUpdateContent={handleUpdatePromptContent}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        isOpen={isDeleteConfirmOpen}
        onClose={() => setIsDeleteConfirmOpen(false)}
        onConfirm={handleConfirmDelete}
        promptName={promptToDelete?.name || ''}
      />
    </DndProvider>
  );
};

// Apply React.memo and export
export const ManagePromptsModalContainer = memo(ManagePromptsModalComponent, (prevProps, nextProps) => {
  // Custom comparison function for React.memo
  const isEqual = prevProps.isOpen === nextProps.isOpen &&
                  prevProps.onClose === nextProps.onClose;

  if (!isEqual) {
    console.log('[ManagePromptsModal memo] Props are NOT equal, re-rendering. Prev:', prevProps, 'Next:', nextProps);
  }
  return isEqual;
});
