"use client"

import { useToast } from "@renderer/hooks/use-toast"
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastViewport,
} from "./toast"

export function Toaster() {
  const { toasts } = useToast()

  return (
    <ToastProvider>
      {toasts.map(({ id, description, variant, ...props }) => (
        <Toast
          key={id}
          {...props}
          variant={variant}
          className="flex items-center"
        >
          <ToastClose />
          <div className="flex-1 text-left">
            <ToastDescription>{description}</ToastDescription>
          </div>
        </Toast>
      ))}
      <ToastViewport />
    </ToastProvider>
  )
}
