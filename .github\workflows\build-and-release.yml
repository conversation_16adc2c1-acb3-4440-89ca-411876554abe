name: Build and Release

on:
  push:
    tags:
      - 'v*' # Triggers when a tag starting with 'v' is pushed (e.g., v1.0.0)
  workflow_dispatch: # Allows manual triggering

jobs:
  build-and-release:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Build and publish Electron app (Windows)
        if: matrix.os == 'windows-latest'
        run: npm run build:win:publish
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and publish Electron app (macOS)
        if: matrix.os == 'macos-latest'
        run: npm run build:mac:publish
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and publish Electron app (Linux)
        if: matrix.os == 'ubuntu-latest'
        run: npm run build:linux:publish
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  create-release:
    needs: build-and-release
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          draft: false
          prerelease: false
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
