# Auto-Update Implementation Guide

This document explains how TruffleNote's automatic update system works using GitHub Releases and `electron-updater`.

## Overview

TruffleNote uses GitHub Releases as the update provider, which offers several advantages:
- Free for both public and private repositories
- Integrated with your existing GitHub workflow
- Automatic release asset management
- Built-in versioning and changelog support

## How It Works

### 1. Build Process
When you build TruffleNote for release:
1. `electron-builder` creates platform-specific installers (.exe, .dmg, .AppImage, etc.)
2. It uploads these to a GitHub Release along with update metadata files (`latest.yml`, `latest-mac.yml`, etc.)
3. The release contains all necessary information for the auto-updater

### 2. Update Checking
The TruffleNote application:
1. Checks for updates on startup (after a 3-second delay)
2. Uses `electron-updater` to query the GitHub Releases API
3. Compares the latest release version with the current application version
4. Shows native dialogs to prompt users when updates are available

### 3. Update Installation
When an update is available:
1. User is prompted with update information and release notes
2. If accepted, the update is downloaded in the background
3. Once downloaded, user is prompted to restart and install
4. The update is applied during the restart process

## Configuration Files

### `electron-builder.yml`
```yaml
publish:
  provider: github
  owner: pantheonnetwork
  repo: trufflenote
  private: true
  releaseType: release
```

### `dev-app-update.yml` (for development)
Used for testing auto-updates in development mode.

## Usage

### For Developers

#### Building for Release
```bash
# Build for specific platform with auto-publish
npm run build:win:publish
npm run build:mac:publish
npm run build:linux:publish

# Build for all platforms
npm run build:all:publish
```

#### Required Environment Variables
- `GH_TOKEN`: GitHub Personal Access Token with `repo` scope

#### Setting up GitHub Token
1. Go to GitHub Settings > Developer settings > Personal access tokens
2. Generate a new token with `repo` scope
3. Set it as an environment variable: `GH_TOKEN=your_token_here`
4. Or use it in CI/CD with GitHub Secrets

### For CI/CD (GitHub Actions)

The project includes a GitHub Actions workflow (`.github/workflows/build-and-release.yml`) that:
1. Triggers on version tags (e.g., `v1.0.0`)
2. Builds for all platforms simultaneously
3. Publishes releases automatically
4. Uses the built-in `GITHUB_TOKEN` (no manual setup required)

#### Creating a Release
1. Update version in `package.json`
2. Commit changes: `git commit -am "Release v1.0.0"`
3. Create and push tag: `git tag v1.0.0 && git push origin v1.0.0`
4. GitHub Actions will automatically build and create the release

### For End Users

#### Automatic Updates
- TruffleNote checks for updates automatically on startup
- Users receive native notifications when updates are available
- Updates can be downloaded and installed with user consent

#### Manual Update Check
- Use the info button in the title bar
- Select "Check for Updates" from the dropdown menu
- Or use the application menu: TruffleNote > Check for Updates

## Security Considerations

### For Private Repositories
- The app uses the same token configured during build time
- `electron-updater` handles authentication transparently
- No tokens are embedded in the client application

### Update Verification
- Updates are downloaded from GitHub's secure servers
- `electron-updater` verifies checksums automatically
- Only signed releases (if code signing is configured) are accepted

## Troubleshooting

### Common Issues

#### Update Check Fails
- Verify GitHub repository access
- Check network connectivity
- Ensure the repository has published releases

#### "No Updates Available"
- Verify version in `package.json` matches release tags
- Check that releases are published (not drafts)
- Ensure release assets include the correct platform files

#### Development Mode
- Auto-updater is disabled in development mode
- Use `npm run build` and test with the built application

### Debugging
- Check the console for auto-updater logs
- Enable verbose logging in `autoUpdater.ts`
- Verify GitHub Release assets include `latest.yml` files

## File Structure

```
src/main/
├── autoUpdater.ts          # Auto-updater logic and UI
├── index.ts               # Main process integration
└── ...

src/renderer/src/components/customUi/
├── UpdateStatus.tsx       # Update status UI component
└── ...

.github/workflows/
└── build-and-release.yml # CI/CD for automated releases

electron-builder.yml      # Build and publish configuration
```

## Future Enhancements

- [ ] Update progress indicators in the UI
- [ ] Beta/prerelease channel support
- [ ] Forced update mechanism for critical security patches
- [ ] Background update downloads
- [ ] Rollback capability for failed updates

## Related Documentation

- [electron-updater Documentation](https://www.electron.build/auto-update)
- [GitHub Releases API](https://docs.github.com/en/rest/releases)
- [electron-builder Configuration](https://www.electron.build/configuration/configuration)
