import { contextBridge, ipc<PERSON>ender<PERSON> } from 'electron'
import { FileStorageAPI, ProjectSettings, FrontendStoredFile, DocumentSettings, BlockSaveOperation, BlockBatchSaveResult, BlockLoadResult, DocumentBlocksMetadata, SavedParagraphMetadata, SavedParagraphCategory } from '../renderer/types/global'

// Create a minimal electron API instead of using @electron-toolkit/preload
const electronAPI = {
  ipcRenderer: {
    send: (channel: string, ...args: any[]) => ipcRenderer.send(channel, ...args),
    on: (channel: string, listener: (event: any, ...args: any[]) => void) => {
      const subscription = (event: any, ...args: any[]) => listener(event, ...args)
      ipcRenderer.on(channel, subscription)
      return () => ipcRenderer.removeListener(channel, subscription)
    },
    off: (channel: string, listener: any) => ipcRenderer.off(channel, listener),
    invoke: (channel: string, ...args: any[]) => ipcRenderer.invoke(channel, ...args),
    removeAllListeners: (channel: string) => ipcRenderer.removeAllListeners(channel)
  },
  process: {
    platform: process.platform,
    versions: process.versions
  }
}

// Custom APIs for renderer
const fileStorageApiBridge: FileStorageAPI = {
  chooseStorageDirectory: () => ipcRenderer.invoke('fileStorage:chooseStorageDirectory'),
  getStoragePath: () => ipcRenderer.invoke('fileStorage:getStoragePath'),
  clearStoragePath: () => ipcRenderer.invoke('fileStorage:clearStoragePath'),
  getFiles: () => ipcRenderer.invoke('fileStorage:getFiles'),
  createDocument: (documentName: string, parentId?: string | null) => ipcRenderer.invoke('fileStorage:createDocument', documentName, parentId),
  loadProjectSettings: () => ipcRenderer.invoke('fileStorage:loadProjectSettings'),
  saveProjectSettings: (settings: ProjectSettings) => ipcRenderer.invoke('fileStorage:saveProjectSettings', settings),
  getFileById: (id: string) => ipcRenderer.invoke('fileStorage:getFileById', id),
  saveDocumentContent: (docId: string, content: string) => ipcRenderer.invoke('fileStorage:saveDocumentContent', docId, content),
  saveDocumentMetadata: (docId: string, metadata: DocumentSettings) => ipcRenderer.invoke('fileStorage:saveDocumentMetadata', docId, metadata),

  // File management operations
  deleteFile: (id: string) => ipcRenderer.invoke('fileStorage:deleteFile', id),
  renameFile: (fileId: string, newName: string) => ipcRenderer.invoke('fileStorage:renameFile', fileId, newName),
  duplicateFile: (fileIdToDuplicate: string, suggestedNewName: string) => ipcRenderer.invoke('fileStorage:duplicateFile', fileIdToDuplicate, suggestedNewName),
  createFolder: (folderName: string, parentId?: string | null) => ipcRenderer.invoke('fileStorage:createFolder', folderName, parentId),
  exportFile: (fileToExport: FrontendStoredFile) => ipcRenderer.invoke('fileStorage:exportFile', fileToExport),
  moveItem: (itemId: string, newParentId: string | null) => ipcRenderer.invoke('fileStorage:moveItem', itemId, newParentId),

  // Block-based operations
  saveDocumentBlocks: (docId: string, blocks: BlockSaveOperation[]) => ipcRenderer.invoke('fileStorage:saveDocumentBlocks', docId, blocks),
  loadDocumentBlocks: (docId: string, blockIds?: string[]) => ipcRenderer.invoke('fileStorage:loadDocumentBlocks', docId, blockIds),
  deleteDocumentBlocks: (docId: string, blockIds: string[]) => ipcRenderer.invoke('fileStorage:deleteDocumentBlocks', docId, blockIds),
  saveDocumentBlocksMetadata: (docId: string, metadata: DocumentBlocksMetadata) => ipcRenderer.invoke('fileStorage:saveDocumentBlocksMetadata', docId, metadata),
  loadDocumentBlocksMetadata: (docId: string) => ipcRenderer.invoke('fileStorage:loadDocumentBlocksMetadata', docId),
  isDocumentBlockBased: (docId: string) => ipcRenderer.invoke('fileStorage:isDocumentBlockBased', docId),
  migrateDocumentToBlocks: (docId: string) => ipcRenderer.invoke('fileStorage:migrateDocumentToBlocks', docId),

  // Saved Paragraphs methods
  getSavedParagraphs: () => ipcRenderer.invoke('fileStorage:getSavedParagraphs'),
  getSavedParagraphById: (id: string) => ipcRenderer.invoke('fileStorage:getSavedParagraphById', id),
  saveParagraph: (metadata: Omit<SavedParagraphMetadata, 'id' | 'createdAt' | 'updatedAt'>, content: any) =>
    ipcRenderer.invoke('fileStorage:saveParagraph', metadata, content),
  updateParagraph: (id: string, metadata: Partial<SavedParagraphMetadata>, content?: any) =>
    ipcRenderer.invoke('fileStorage:updateParagraph', id, metadata, content),
  deleteParagraph: (id: string) => ipcRenderer.invoke('fileStorage:deleteParagraph', id),

  getSavedParagraphCategories: () => ipcRenderer.invoke('fileStorage:getSavedParagraphCategories'),
  saveParagraphCategory: (category: Omit<SavedParagraphCategory, 'id' | 'createdAt' | 'updatedAt'>) =>
    ipcRenderer.invoke('fileStorage:saveParagraphCategory', category),
  updateParagraphCategory: (id: string, category: Partial<SavedParagraphCategory>) =>
    ipcRenderer.invoke('fileStorage:updateParagraphCategory', id, category),
  deleteParagraphCategory: (id: string) => ipcRenderer.invoke('fileStorage:deleteParagraphCategory', id),
}

// App API for auto-updater and other app functions
const appApiBridge = {
  checkForUpdates: () => ipcRenderer.invoke('app:checkForUpdates'),
  getVersion: () => ipcRenderer.invoke('app:getVersion'),
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('fileStorage', fileStorageApiBridge)
    contextBridge.exposeInMainWorld('app', appApiBridge)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.fileStorage = fileStorageApiBridge
  // @ts-ignore (define in dts)
  window.app = appApiBridge
}
