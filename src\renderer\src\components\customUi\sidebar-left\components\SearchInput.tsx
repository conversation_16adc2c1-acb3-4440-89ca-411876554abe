import React from 'react';
import { Search, X } from 'lucide-react';
import { SearchInputProps } from '../types';

export const SearchInput = React.memo(({ searchTerm, setSearchTerm }: SearchInputProps) => {
  return (
    <div className="p-2 border-b border-border">
      <div className="relative">
        <Search className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
        <input
          type="text"
          placeholder="Search files..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full h-8 pl-8 pr-8 rounded-md border border-input bg-background text-sm"
        />
        {searchTerm && (
          <button
            className="absolute right-2 top-1/2 -translate-y-1/2 h-5 w-5 flex items-center justify-center"
            onClick={() => setSearchTerm('')}
          >
            <X className="h-3.5 w-3.5 text-gray-400" />
          </button>
        )}
      </div>
    </div>
  );
});

SearchInput.displayName = 'SearchInput';
