import React from 'react';
import { Label } from '@/ui/label';
import { Switch } from '@/ui/switch';
import { Button } from '@/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/ui/select';
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from '@/ui/tooltip';
import { Separator } from '@/ui/separator';
import { DocumentSettings, ProjectSettings } from '@/types/global';
import { DocumentSettingsSectionProps } from './SettingsSidebarTypes';
import { useNotification } from '@/contexts/NotificationContext';
import { ManageApiKeysModal } from '../manage-api-keys-modal';
import { useSettingsSidebar } from './SettingsSidebarContext';

export function SettingsSidebarDocumentSettings({
  activeFile,
  projectSettings,
  documentSettings,
  onSaveActiveDocumentSettings,
  onUpdateProjectSettings,
  setIsUiExtensionOpen
}: DocumentSettingsSectionProps) {
  const { showNotification } = useNotification();
  const { isApiKeysModalOpen, setIsApiKeysModalOpen } = useSettingsSidebar();

  // Handle modal open/close - context automatically manages UI extension state
  const handleOpenApiKeysModal = React.useCallback(() => {
    setIsApiKeysModalOpen(true);
  }, [setIsApiKeysModalOpen]);

  const handleCloseApiKeysModal = React.useCallback(() => {
    setIsApiKeysModalOpen(false);
  }, [setIsApiKeysModalOpen]);

  const memoizedOnUpdateProjectSettings = React.useCallback((updatedSettings: ProjectSettings) => {
    return onUpdateProjectSettings(updatedSettings);
  }, [onUpdateProjectSettings]);

  const handleToggleChange = async (key: keyof DocumentSettings, value: boolean) => {
    if (!activeFile || !documentSettings) {
      showNotification("No active document to update.", "error");
      return;
    }
    const newSettings = { ...documentSettings, [key]: value };
    try {
      await onSaveActiveDocumentSettings(newSettings);
      showNotification('Setting updated.', 'success');
    } catch (error) {
      showNotification('Failed to update setting.', 'error');
      console.error(`Error saving ${key}:`, error);
    }
  };

  const handleSelectChange = async (key: keyof DocumentSettings, value: string | null) => {
    if (!activeFile || !documentSettings) {
      showNotification("No active document to update.", "error");
      return;
    }
    const newSettings = { ...documentSettings, [key]: value };
    try {
      await onSaveActiveDocumentSettings(newSettings);
      showNotification('Selection updated.', 'success');
    } catch (error) {
      showNotification('Failed to update selection.', 'error');
      console.error(`Error saving ${key}:`, error);
    }
  };

  return (
    <>
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h4 className="font-medium text-sm text-foreground">Re-styling</h4>
          <TooltipProvider delayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="link"
                  size="sm"
                  className="text-xs h-auto p-0 text-foreground"
                  onClick={handleOpenApiKeysModal}
                >
                  Manage API Keys
                </Button>
              </TooltipTrigger>
              <TooltipContent side="left">
                <p>Open API key management modal</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <Select
          value={documentSettings?.selectedRestyleConfigId || 'none'}
          onValueChange={(value) => handleSelectChange('selectedRestyleConfigId', value === 'none' ? null : value)}
          disabled={!projectSettings || projectSettings.restyleConfigs.length === 0}
          onOpenChange={(open) => {
            if (open) setIsUiExtensionOpen(true);
          }}
        >
          <SelectTrigger id="restyle-selector" className="dark:bg-slate-800 dark:border-slate-700 dark:text-slate-300">
            <SelectValue placeholder="Select a re-styling configuration" />
          </SelectTrigger>
          <SelectContent className="dark:bg-slate-800 dark:border-slate-700">
            <SelectItem value="none" className="dark:text-slate-300 dark:focus:bg-slate-700">None</SelectItem>
            {projectSettings?.restyleConfigs
              .filter((config: any) => config.isVisible !== false)
              .map((config: any) => (
                <SelectItem key={config.id} value={config.id} className="dark:text-slate-300 dark:focus:bg-slate-700">
                  {config.name}
                </SelectItem>
              ))}
          </SelectContent>
        </Select>
      </div>

      <Separator className="dark:bg-slate-700"/>

      <div className="space-y-2">
        <h4 className="font-medium text-sm text-foreground">Editor</h4>
        <div className="flex items-center justify-between p-2 border border-foreground/50 hover:border-foreground/30 transition-colors rounded-md">
          <Label htmlFor="hyperlink-saving" className="text-xs cursor-pointer flex-grow pr-2 text-muted-foreground">
            Save & Reapply Hyperlinks on Paste
          </Label>
          <TooltipProvider delayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Switch
                  className="custom-switch"
                  id="hyperlink-saving"
                  checked={documentSettings?.hyperlinkSavingEnabled || false}
                  onCheckedChange={(checked) => handleToggleChange('hyperlinkSavingEnabled', checked)}
                />
              </TooltipTrigger>
              <TooltipContent side="left">
                <p>Preserve hyperlinks when pasting content</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <Separator className="dark:bg-slate-700"/>

      {/* Manage API Keys Modal */}
      <ManageApiKeysModal
        isOpen={isApiKeysModalOpen}
        onClose={handleCloseApiKeysModal}
        projectSettings={projectSettings}
        onUpdateProjectSettings={memoizedOnUpdateProjectSettings}
      />
    </>
  );
}
