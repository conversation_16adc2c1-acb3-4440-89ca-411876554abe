// Main modal container export
export { ManageSavedParagraphsModalContainer } from './ManageSavedParagraphsModalContainer';

// Type exports
export type {
  ManageSavedParagraphsModalProps,
  SavedParagraphMetadata,
  SavedParagraphCategory,
  ParagraphWithContent,
  ModalView
} from './types';

// Hook exports (if needed externally)
export { useParagraphManagement } from './hooks/useParagraphManagement';
export { useCategoryManagement } from './hooks/useCategoryManagement';
