import React from 'react';
import { SidebarContentProps } from '../types';
import { NodeItem } from './NodeItem';
import { RootDropArea } from './RootDropArea';

export const SidebarContent = React.memo(({
  selectedTab,
  currentStoragePath,
  loading,
  filteredFileTree,
  recentFiles,
  bookmarkedFiles,
  searchTerm,
  currentDocId,
  onFileOpen,
  onToggleExpansion,
  onCreateFile,
  onCreateFolder,
  onRename,
  onDelete,
  onToggleBookmark,
  onItemMove
}: SidebarContentProps) => {
  const renderFilesTab = () => {
    if (!currentStoragePath) {
      return (
        <div className="text-center p-4 text-gray-500 dark:text-gray-400 text-sm">
          Please select a storage folder to view files.
        </div>
      );
    }

    if (loading) {
      return (
        <div className="flex justify-center items-center h-20">
          <div className="animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full" />
        </div>
      );
    }

    if (filteredFileTree.length === 0 && !searchTerm) {
      return (
        <div className="text-center p-4 text-gray-500 dark:text-gray-400 text-sm">
          No files or folders. Use the + buttons above to create new items.
        </div>
      );
    }

    if (filteredFileTree.length === 0 && searchTerm) {
      return (
        <div className="text-center p-4 text-gray-500 dark:text-gray-400 text-sm">
          No items found for "{searchTerm}"
        </div>
      );
    }

    return (
      <>
        {filteredFileTree.map(node => (
          <NodeItem
            key={node.id}
            node={node}
            level={0}
            currentDocId={currentDocId}
            bookmarkedFiles={bookmarkedFiles}
            onFileOpen={onFileOpen}
            onToggleExpansion={onToggleExpansion}
            onCreateFile={onCreateFile}
            onCreateFolder={onCreateFolder}
            onRename={onRename}
            onDelete={onDelete}
            onToggleBookmark={onToggleBookmark}
            onItemMove={onItemMove}
          />
        ))}
        {/* Add RootDropArea below the list of files and folders */}
        {currentStoragePath && filteredFileTree.length > 0 && (
          <RootDropArea onItemMove={onItemMove} />
        )}
      </>
    );
  };

  const renderRecentTab = () => {
    if (recentFiles.length === 0) {
      return (
        <div className="text-center p-4 text-gray-500 dark:text-gray-400 text-sm">
          No recent files.
        </div>
      );
    }

    return recentFiles.map(node => (
      <NodeItem
        key={`recent-${node.id}`}
        node={node}
        level={0}
        currentDocId={currentDocId}
        bookmarkedFiles={bookmarkedFiles}
        onFileOpen={onFileOpen}
        onToggleExpansion={onToggleExpansion}
        onCreateFile={onCreateFile}
        onCreateFolder={onCreateFolder}
        onRename={onRename}
        onDelete={onDelete}
        onToggleBookmark={onToggleBookmark}
        onItemMove={onItemMove}
      />
    ));
  };

  const renderBookmarksTab = () => {
    if (bookmarkedFiles.length === 0) {
      return (
        <div className="text-center p-4 text-gray-500 dark:text-gray-400 text-sm">
          No bookmarked files.
        </div>
      );
    }

    return bookmarkedFiles.map(node => (
      <NodeItem
        key={`bookmark-${node.id}`}
        node={node}
        level={0}
        currentDocId={currentDocId}
        bookmarkedFiles={bookmarkedFiles}
        onFileOpen={onFileOpen}
        onToggleExpansion={onToggleExpansion}
        onCreateFile={onCreateFile}
        onCreateFolder={onCreateFolder}
        onRename={onRename}
        onDelete={onDelete}
        onToggleBookmark={onToggleBookmark}
        onItemMove={onItemMove}
      />
    ));
  };

  return (
    <div className="flex-1 overflow-auto p-2">
      {selectedTab === 'files' && renderFilesTab()}
      {selectedTab === 'recent' && renderRecentTab()}
      {selectedTab === 'bookmarks' && renderBookmarksTab()}
    </div>
  );
});

SidebarContent.displayName = 'SidebarContent';
