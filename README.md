# TruffleNote

A modern note-taking application built with Electron, React, and TypeScript, featuring automatic updates via GitHub Releases.

## Recommended IDE Setup

- [VSCode](https://code.visualstudio.com/) + [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint) + [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)

## Project Setup

### Install

```bash
$ yarn
```

### Development

```bash
$ yarn dev
```

### Build

```bash
# Local builds (creates installers, no publishing)
$ yarn build:win    # Windows .exe
$ yarn build:mac    # macOS .dmg
$ yarn build:linux  # Linux .AppImage/.deb/.snap
```

### Publish Builds (with Auto-Updates)

```bash
# Set GitHub token first
$ set GH_TOKEN=your_github_token_here

# Build and publish to GitHub Releases
$ yarn build:win:publish
$ yarn build:mac:publish
$ yarn build:linux:publish
```

### Automated Releases

Push a version tag to trigger automated builds via GitHub Actions:

```bash
$ git tag v1.0.0
$ git push origin v1.0.0
```

## Auto-Update System

TruffleNote includes automatic updates powered by GitHub Releases:

- **Automatic**: Checks for updates on startup
- **Manual**: Use the info button in title bar → "Check for Updates"
- **Secure**: Updates verified through GitHub's infrastructure
- **User Control**: All updates require user consent

For detailed setup instructions, see [docs/AUTO_UPDATE_SETUP.md](docs/AUTO_UPDATE_SETUP.md).
