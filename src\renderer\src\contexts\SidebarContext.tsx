import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

interface SidebarContextType {
  isLeftSidebarExpanded: boolean;
  setLeftSidebarExpanded: (expanded: boolean) => void;
  isRightSidebarExpanded: boolean;
  setRightSidebarExpanded: (expanded: boolean) => void;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

export function SidebarProvider({ children }: { children: ReactNode }) {
  const [isLeftSidebarExpanded, setIsLeftSidebarExpanded] = useState(false);
  const [isRightSidebarExpanded, setIsRightSidebarExpanded] = useState(false);

  const setLeftSidebarExpanded = useCallback((expanded: boolean) => {
    setIsLeftSidebarExpanded(expanded);
    // Close right sidebar when left sidebar opens
    if (expanded) {
      setIsRightSidebarExpanded(false);
    }
  }, []);

  const setRightSidebarExpanded = useCallback((expanded: boolean) => {
    setIsRightSidebarExpanded(expanded);
    // Close left sidebar when right sidebar opens
    if (expanded) {
      setIsLeftSidebarExpanded(false);
    }
  }, []);

  return (
    <SidebarContext.Provider value={{
      isLeftSidebarExpanded,
      setLeftSidebarExpanded,
      isRightSidebarExpanded,
      setRightSidebarExpanded
    }}>
      {children}
    </SidebarContext.Provider>
  );
}

export function useSidebarContext() {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error('useSidebarContext must be used within a SidebarProvider');
  }
  return context;
}
