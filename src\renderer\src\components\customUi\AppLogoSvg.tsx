import React from 'react';

interface AppLogoSvgProps extends React.SVGProps<SVGSVGElement> {
  fillColor?: string;
  strokeColor?: string;
  strokeWidthValue?: number; // Renamed to avoid conflict with SVGAttributes.strokeWidth
  eyeColor?: string;
}

const AppLogoSvg: React.FC<AppLogoSvgProps> = ({
  width = 64, // Default to a size suitable for w-16 h-16
  height = 64, // Default to a size suitable for w-16 h-16
  fillColor = "#F6E3C3",
  strokeColor = "#1E1E1E",
  strokeWidthValue = 1.5, // Adjusted default for smaller size
  eyeColor = "#1E1E1E",
  className = "",
  ...props
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 1225 1227"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      {/* Main body shape */}
      <path
        d="M46.432 578.118C58.432 560.518 82.0987 574.785 92.432 584.118C235.268 652.721 380.044 700.07 513.432 732.09C479.428 701.268 460.432 659.617 460.432 628.117C456.932 587.617 492.194 556.117 494.432 603.117C497.232 661.917 536.601 693.951 555.936 702.617C578.967 656.757 620.78 597.585 643.432 567.931C638.785 567.744 634.405 567.097 630.432 566.117C601.432 551.617 585.932 540.117 603.932 461.117C617.921 399.718 709.432 327.117 751.932 298.118C848.732 245.718 929.598 307.951 957.932 345.618C974.104 367.117 989.432 411.117 1029.43 474.118C1041.93 556.618 945.098 587.784 902.932 594.618C921.332 683.818 910.598 762.36 902.932 790.481C954.481 793.289 997.466 793.304 1029.43 791.618C1083.43 786.618 1198.05 760.618 1179.43 791.618C1076.43 963.118 897.432 959.118 693.932 940.618C490.432 922.118 298.432 753.618 265.432 719.618C239.032 692.418 213.765 677.618 204.432 673.618L69.432 613.618C56.7654 609.118 34.432 595.718 46.432 578.118Z"
        fill={fillColor}
      />
      
      {/* Outline strokes */}
      <path
        d="M902.932 790.481C954.481 793.289 997.466 793.304 1029.43 791.618C1083.43 786.618 1198.05 760.618 1179.43 791.618C1076.43 963.118 897.432 959.118 693.932 940.618C490.432 922.118 298.432 753.618 265.432 719.618C239.032 692.418 213.765 677.618 204.432 673.618L69.432 613.618C56.7654 609.118 34.432 595.718 46.432 578.118C58.432 560.518 82.0987 574.785 92.432 584.118C235.268 652.721 380.044 700.07 513.432 732.09M902.932 790.481C910.598 762.36 921.332 683.818 902.932 594.618C945.098 587.784 1041.93 556.618 1029.43 474.118C989.432 411.117 974.104 367.117 957.932 345.618C929.598 307.951 848.732 245.718 751.932 298.118C709.432 327.117 617.921 399.718 603.932 461.117C585.932 540.117 601.432 551.617 630.432 566.117C634.405 567.097 638.785 567.744 643.432 567.931M902.932 790.481C881.665 789.322 858.941 787.688 834.932 785.503M733.932 402.117C739.647 538.938 682.246 569.492 643.432 567.931M643.432 567.931C620.78 597.585 578.967 656.757 555.936 702.617M541.432 738.627C543.983 728.515 549.142 716.145 555.936 702.617M541.432 738.627C532.158 736.523 522.823 734.344 513.432 732.09M541.432 738.627C603.088 752.619 662.044 763.344 716.932 771.409M555.936 702.617C536.601 693.951 497.232 661.917 494.432 603.117C492.194 556.117 456.932 587.617 460.432 628.117C460.432 659.617 479.428 701.268 513.432 732.09M716.932 771.409C720.765 753.978 717.032 716.417 671.432 705.617M716.932 771.409C731.737 773.584 746.245 775.566 760.432 777.367M760.432 777.367C760.598 762.617 759.532 728.917 753.932 712.117M760.432 777.367C786.402 780.663 811.29 783.35 834.932 785.503M834.932 785.503C839.265 778.541 846.632 750.917 841.432 696.117"
        stroke={strokeColor}
        strokeWidth={strokeWidthValue} // Use the new prop name
        fill="none"
      />
      
      {/* Eyes */}
      <ellipse cx="932.932" cy="424.117" rx="15" ry="19.5" fill={eyeColor} />
      <ellipse cx="823.932" cy="430.617" rx="16" ry="19" fill={eyeColor} />
      
      {/* Nose */}
      <ellipse cx="904.432" cy="501.117" rx="28.5" ry="21.5" fill={eyeColor} />
    </svg>
  );
};

export default AppLogoSvg; 