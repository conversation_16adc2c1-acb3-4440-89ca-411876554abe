import React, { useState, useEffect } from 'react';
import AppLogoSvg from './AppLogoSvg';
import { useTheme } from '@renderer/hooks/useTheme';
import { useDocumentStore } from '@renderer/stores/blockDocumentStore';

// Component-specific color schemes for the placeholder
const placeholderLightModeColorSchemes = [
  { body: "#F5F1E8", eyes: "#8B4513" },
  { body: "#FFF5F0", eyes: "#A0522D" },
  { body: "#F0F8E8", eyes: "#556B2F" },
  { body: "#FFF8DC", eyes: "#CD853F" },
  { body: "#F5F0FF", eyes: "#6B46C1" },
  { body: "#F0F9FF", eyes: "#1E40AF" },
  { body: "#FDF2F8", eyes: "#BE185D" },
  { body: "#F0FDF9", eyes: "#047857" },
  { body: "#FFFBEB", eyes: "#D97706" },
  { body: "#F8F4F0", eyes: "#92400E" },
];

const placeholderDarkModeColorSchemes = [
  { body: "#3E3629", eyes: "#D4B896" },
  { body: "#2F2B3A", eyes: "#C8A2C8" },
  { body: "#1F3A2E", eyes: "#90EE90" },
  { body: "#4A3728", eyes: "#DEB887" },
  { body: "#2D2A47", eyes: "#B19CD9" },
  { body: "#2B3D3D", eyes: "#20B2AA" },
  { body: "#3D1A2E", eyes: "#FFB6C1" },
  { body: "#2B3D2F", eyes: "#98FB98" },
  { body: "#473829", eyes: "#F4A460" },
  { body: "#2E2A47", eyes: "#DDA0DD" },
];

interface PlaceholderLogoColors {
  fill: string;
  stroke: string;
  eyes: string;
}

export function NoDocumentPlaceholder() {
  const { isDarkMode } = useTheme();
  const { selectStorageDirectory } = useDocumentStore();

  const [logoColors, setLogoColors] = useState<PlaceholderLogoColors>(() => {
    const initialSchemes = isDarkMode ? placeholderDarkModeColorSchemes : placeholderLightModeColorSchemes;
    const defaultBase = initialSchemes[0] || { body: '#F5F1E8', eyes: '#8B4513' };
    return {
      fill: defaultBase.body,
      stroke: defaultBase.eyes,
      eyes: defaultBase.eyes,
    };
  });

  const selectPlaceholderRandomScheme = (currentIsDark: boolean): PlaceholderLogoColors => {
    const schemes = currentIsDark ? placeholderDarkModeColorSchemes : placeholderLightModeColorSchemes;
    const randomBase = schemes[Math.floor(Math.random() * schemes.length)];
    return {
      fill: randomBase.body,
      eyes: randomBase.eyes,
      stroke: randomBase.eyes,
    };
  };

  useEffect(() => {
    setLogoColors(selectPlaceholderRandomScheme(isDarkMode));
  }, [isDarkMode]);

  const handleSelectStorageFolder = async () => {
    try {
      await selectStorageDirectory();
    } catch (error) {
      console.error('Error selecting storage directory:', error);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center h-full text-center p-10 bg-background">
      <div className="p-6 bg-card rounded-xl shadow-sm border border-border/50 max-w-lg w-full">
        <div className="flex justify-center mb-6">
          <AppLogoSvg
            className="w-24 h-24"
            fillColor={logoColors.fill}
            strokeColor={logoColors.stroke}
            eyeColor={logoColors.eyes}
            strokeWidthValue={30}
          />
        </div>
        <h2 className="text-2xl font-semibold mb-4 text-foreground">
          Welcome! Let's Get Started
        </h2>
        <p className="text-md text-muted-foreground mb-3">
          To begin, please set your storage folder. This is where your documents will be saved.
        </p>
        <button
          onClick={handleSelectStorageFolder}
          className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors mt-2 mb-4"
        >
          Select Storage Folder
        </button>
        <p className="text-sm text-muted-foreground/80">
          If you haven't created a folder yet, create a new one on your
          computer in a location you'll easily remember.
        </p>
      </div>
    </div>
  );
}
