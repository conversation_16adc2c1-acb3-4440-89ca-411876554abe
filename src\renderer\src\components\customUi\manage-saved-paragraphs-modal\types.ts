import { SavedParagraphMetadata, SavedParagraphCategory } from '@/types/global';

// Re-export the global types for consistency
export type { SavedParagraphMetadata, SavedParagraphCategory };

// Modal specific types
export interface ManageSavedParagraphsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export interface ParagraphWithContent {
  metadata: SavedParagraphMetadata;
  content: any;
}

export interface ParagraphCardProps {
  paragraph: SavedParagraphMetadata;
  category: SavedParagraphCategory | undefined;
  onEdit: (paragraph: SavedParagraphMetadata) => void;
  onDelete: (paragraph: SavedParagraphMetadata) => void;
  onInsert: (paragraph: SavedParagraphMetadata) => void;
}

export interface CategoryManagerProps {
  categories: SavedParagraphCategory[];
  onCreateCategory: (name: string, color?: string) => void;
  onUpdateCategory: (id: string, updates: Partial<SavedParagraphCategory>) => void;
  onDeleteCategory: (id: string) => void;
}

export interface ParagraphFormProps {
  initialParagraph?: ParagraphWithContent;
  categories: SavedParagraphCategory[];
  onSave: (title: string, categoryId: string, content: any, description?: string, tags?: string[]) => void;
  onCancel: () => void;
}

export interface SearchFilterProps {
  searchQuery: string;
  selectedCategoryIds: string[];
  categories: SavedParagraphCategory[];
  onSearchChange: (query: string) => void;
  onCategoryChange: (categoryIds: string[]) => void;
}

// View types for modal navigation
export type ModalView = 'list' | 'create' | 'edit' | 'category-management';

// Constants
export const CONTENT_LENGTH_LIMIT = 10000; // 3 pages approximate character limit
