import React from 'react';
import { PlusCircleIcon } from 'lucide-react';
import { Input } from '@/ui/input';
import { Button } from '@/ui/button';
import { ScrollArea } from '@/ui/scroll-area';
import { DraggablePromptItem } from './DraggablePromptItem';
import { TabContentProps } from '../types';

export const GlobalPromptsTab: React.FC<TabContentProps> = ({
  prompts,
  newPromptName,
  setNewPromptName,
  onCreatePrompt,
  movePrompt,
  activeDocumentId,
  activeDocumentSettings,
  handleVisibilityToggle,
  handleInitiateDelete,
  handleUpdatePromptContent,
  handleUpdatePromptName,
  projectSettingsSystemPrompts,
  setPromptToEdit,
  setExpandedEditorOpen,
  promptActions
}) => {
  return (
    <>
      <div className="mb-4 flex gap-2 items-center">
        <Input
          type="text"
          placeholder="New global prompt name..."
          value={newPromptName}
          onChange={(e) => setNewPromptName(e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && onCreatePrompt()}
          className="flex-grow max-w-md text-text-foreground"
        />
        <Button onClick={onCreatePrompt} variant="outline" size="sm">
          <PlusCircleIcon className="mr-2 h-4 w-4" /> Create Global Prompt
        </Button>
      </div>

      {prompts.length > 0 ? (
        <ScrollArea className="h-[calc(100%-60px)] pr-3">
          {prompts.map((prompt, index) => (
            <DraggablePromptItem
              key={prompt.id}
              prompt={prompt}
              index={index}
              movePrompt={movePrompt}
              activeDocumentId={activeDocumentId}
              activeDocumentSettings={activeDocumentSettings}
              handleVisibilityToggle={handleVisibilityToggle}
              handleInitiateDelete={handleInitiateDelete}
              handleUpdatePromptContent={handleUpdatePromptContent}
              handleUpdatePromptName={handleUpdatePromptName}
              onForkToLocal={promptActions?.onForkToLocal ? () => promptActions.onForkToLocal!(prompt) : undefined}
              projectSettingsSystemPrompts={projectSettingsSystemPrompts}
              setPromptToEdit={setPromptToEdit}
              setExpandedEditorOpen={setExpandedEditorOpen}
            />
          ))}
        </ScrollArea>
      ) : (
        <p className="text-muted-foreground text-sm text-center py-8">
          No global prompts yet. Create one above.
        </p>
      )}
    </>
  );
};
