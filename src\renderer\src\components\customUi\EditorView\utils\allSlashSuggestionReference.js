const allSlashSuggestionReference = [
    {
      "badge": "Ctrl-Alt-1",
      "key": "heading",
      "title": "Heading 1",
      "subtext": "Top-level heading",
      "aliases": [
        "h",
        "heading1",
        "h1"
      ],
      "group": "Headings",
      "icon": {
        "key": null,
        "ref": null,
        "props": {
          "size": 18
        },
        "_owner": null,
        "_store": {}
      }
    },
    {
      "badge": "Ctrl-Alt-2",
      "key": "heading_2",
      "title": "Heading 2",
      "subtext": "Key section heading",
      "aliases": [
        "h2",
        "heading2",
        "subheading"
      ],
      "group": "Headings",
      "icon": {
        "key": null,
        "ref": null,
        "props": {
          "size": 18
        },
        "_owner": null,
        "_store": {}
      }
    },
    {
      "badge": "Ctrl-Alt-3",
      "key": "heading_3",
      "title": "Heading 3",
      "subtext": "Subsection and group heading",
      "aliases": [
        "h3",
        "heading3",
        "subheading"
      ],
      "group": "Headings",
      "icon": {
        "key": null,
        "ref": null,
        "props": {
          "size": 18
        },
        "_owner": null,
        "_store": {}
      }
    },
    {
      "key": "quote",
      "title": "Quote",
      "subtext": "Quote or excerpt",
      "aliases": [
        "quotation",
        "blockquote",
        "bq"
      ],
      "group": "Basic blocks",
      "icon": {
        "key": null,
        "ref": null,
        "props": {
          "size": 18
        },
        "_owner": null,
        "_store": {}
      }
    },
    {
      "badge": "Ctrl-Shift-7",
      "key": "numbered_list",
      "title": "Numbered List",
      "subtext": "List with ordered items",
      "aliases": [
        "ol",
        "li",
        "list",
        "numberedlist",
        "numbered list"
      ],
      "group": "Basic blocks",
      "icon": {
        "key": null,
        "ref": null,
        "props": {
          "size": 18
        },
        "_owner": null,
        "_store": {}
      }
    },
    {
      "badge": "Ctrl-Shift-8",
      "key": "bullet_list",
      "title": "Bullet List",
      "subtext": "List with unordered items",
      "aliases": [
        "ul",
        "li",
        "list",
        "bulletlist",
        "bullet list"
      ],
      "group": "Basic blocks",
      "icon": {
        "key": null,
        "ref": null,
        "props": {
          "size": 18
        },
        "_owner": null,
        "_store": {}
      }
    },
    {
      "badge": "Ctrl-Shift-9",
      "key": "check_list",
      "title": "Check List",
      "subtext": "List with checkboxes",
      "aliases": [
        "ul",
        "li",
        "list",
        "checklist",
        "check list",
        "checked list",
        "checkbox"
      ],
      "group": "Basic blocks",
      "icon": {
        "key": null,
        "ref": null,
        "props": {
          "size": 18
        },
        "_owner": null,
        "_store": {}
      }
    },
    {
      "badge": "Ctrl-Alt-0",
      "key": "paragraph",
      "title": "Paragraph",
      "subtext": "The body of your document",
      "aliases": [
        "p",
        "paragraph"
      ],
      "group": "Basic blocks",
      "icon": {
        "key": null,
        "ref": null,
        "props": {
          "size": 18
        },
        "_owner": null,
        "_store": {}
      }
    },
    {
      "badge": "Ctrl-Alt-c",
      "key": "code_block",
      "title": "Code Block",
      "subtext": "Code block with syntax highlighting",
      "aliases": [
        "code",
        "pre"
      ],
      "group": "Basic blocks",
      "icon": {
        "key": null,
        "ref": null,
        "props": {
          "size": 18
        },
        "_owner": null,
        "_store": {}
      }
    },
    {
      "key": "table",
      "title": "Table",
      "subtext": "Table with editable cells",
      "aliases": [
        "table"
      ],
      "group": "Advanced",
      "icon": {
        "key": null,
        "ref": null,
        "props": {
          "size": 18
        },
        "_owner": null,
        "_store": {}
      }
    },
    {
      "key": "image",
      "title": "Image",
      "subtext": "Resizable image with caption",
      "aliases": [
        "image",
        "imageUpload",
        "upload",
        "img",
        "picture",
        "media",
        "url"
      ],
      "group": "Media",
      "icon": {
        "key": null,
        "ref": null,
        "props": {
          "size": 18
        },
        "_owner": null,
        "_store": {}
      }
    },
    {
      "key": "video",
      "title": "Video",
      "subtext": "Resizable video with caption",
      "aliases": [
        "video",
        "videoUpload",
        "upload",
        "mp4",
        "film",
        "media",
        "url"
      ],
      "group": "Media",
      "icon": {
        "key": null,
        "ref": null,
        "props": {
          "size": 18
        },
        "_owner": null,
        "_store": {}
      }
    },
    {
      "key": "audio",
      "title": "Audio",
      "subtext": "Embedded audio with caption",
      "aliases": [
        "audio",
        "audioUpload",
        "upload",
        "mp3",
        "sound",
        "media",
        "url"
      ],
      "group": "Media",
      "icon": {
        "key": null,
        "ref": null,
        "props": {
          "size": 18
        },
        "_owner": null,
        "_store": {}
      }
    },
    {
      "key": "file",
      "title": "File",
      "subtext": "Embedded file",
      "aliases": [
        "file",
        "upload",
        "embed",
        "media",
        "url"
      ],
      "group": "Media",
      "icon": {
        "key": null,
        "ref": null,
        "props": {
          "size": 18
        },
        "_owner": null,
        "_store": {}
      }
    },
    {
      "key": "emoji",
      "title": "Emoji",
      "subtext": "Search for and insert an emoji",
      "aliases": [
        "emoji",
        "emote",
        "emotion",
        "face"
      ],
      "group": "Others",
      "icon": {
        "key": null,
        "ref": null,
        "props": {
          "size": 18
        },
        "_owner": null,
        "_store": {}
      }
    }
  ]