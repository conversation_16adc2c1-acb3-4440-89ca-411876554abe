/**
 * Block-Based Auto-Save System for Enhanced Document Store
 *
 * This service implements a block-level auto-save system that:
 * - Only saves modified blocks to minimize I/O operations
 * - Uses atomic writes for data integrity
 * - Provides non-intrusive background saving
 * - Integrates with the enhanced document store's dirty tracking
 * - Supports batch operations for efficiency
 */

import { useDocumentStore } from '../stores/blockDocumentStore';
import { BlockSaveOperation, BlockBatchSaveResult } from '../../types/block-storage';

// Additional types for the auto-save system
export interface BlockAutoSaveConfig {
  debounceMs: number;
  retryAttempts: number;
  retryDelayMs: number;
  batchSize: number;
  maxConcurrentSaves: number;
}

export interface BlockSaveStatusInfo {
  status: 'idle' | 'saving' | 'saved' | 'error';
  lastSaved?: Date;
  error?: string;
  retryCount?: number;
}

// Enhanced result type for internal use
export interface EnhancedBlockBatchSaveResult extends BlockBatchSaveResult {
  results?: any[];
  errors?: any[];
  processedCount?: number;
  successCount?: number;
  errorCount?: number;
}

interface BlockAutoSaveOptions {
  debounceMs: number;
  maxBatchSize: number;
  maxRetries: number;
  retryDelayMs: number;
}

interface SaveOperation {
  blockId: string;
  content: any;
  operation: 'create' | 'update' | 'delete';
  retryCount: number;
  lastAttempt: number;
}

export class BlockAutoSaveManager {
  private options: BlockAutoSaveOptions;
  private saveTimeout: NodeJS.Timeout | null = null;
  private isProcessing = false;
  private pendingSaveOperations: Map<string, SaveOperation> = new Map();
  private failedOperations: Map<string, SaveOperation> = new Map();
  private retryTimeout: NodeJS.Timeout | null = null;
  private currentDocumentId: string | null = null;

  constructor(options: Partial<BlockAutoSaveOptions> = {}) {
    this.options = {
      debounceMs: 2000, // 2 seconds debounce for non-intrusive saving
      maxBatchSize: 10, // Process blocks in small batches
      maxRetries: 3,
      retryDelayMs: 5000,
      ...options
    };
  }

  /**
   * Set the current document being edited
   */
  setCurrentDocument(documentId: string | null) {
    this.currentDocumentId = documentId;

    // Process any pending saves for the previous document
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
      this.saveTimeout = null;
      this.processPendingSaves();
    }
  }

  /**
   * Queue a block for saving with debouncing
   */
  queueBlockSave(blockId: string, content: any, operation: 'create' | 'update' | 'delete' = 'update') {
    // Add to pending operations
    this.pendingSaveOperations.set(blockId, {
      blockId,
      content,
      operation,
      retryCount: 0,
      lastAttempt: 0
    });

    // Remove from failed operations if it was there
    this.failedOperations.delete(blockId);

    // Debounce the save operation
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
    }

    this.saveTimeout = setTimeout(() => {
      this.processPendingSaves();
    }, this.options.debounceMs);
  }

  /**
   * Process pending saves in batches
   */
  private async processPendingSaves() {
    if (this.isProcessing || this.pendingSaveOperations.size === 0) {
      return;
    }

    this.isProcessing = true;
    const store = useDocumentStore.getState();

    try {
      store.setAutoSaving(true);

      // Group operations by document
      const operationsByDocument = this.groupOperationsByDocument();

      for (const [documentId, operations] of operationsByDocument) {
        await this.saveDocumentBlocks(documentId, operations);
      }

      // Clear processed operations
      this.pendingSaveOperations.clear();
      store.updateLastAutoSaveTime();

    } catch (error) {
      console.error('Error during auto-save batch processing:', error);
      this.handleSaveFailure(error);
    } finally {
      this.isProcessing = false;
      store.setAutoSaving(false);
    }

    // Schedule retry for failed operations
    this.scheduleRetry();
  }

  /**
   * Group operations by document for batch processing
   */
  private groupOperationsByDocument(): Map<string, SaveOperation[]> {
    const groups = new Map<string, SaveOperation[]>();
    const store = useDocumentStore.getState();

    for (const operation of this.pendingSaveOperations.values()) {
      // Determine document ID from block content or current document
      let documentId = this.currentDocumentId;

      // Try to get document ID from block metadata
      const blockState = store.getBlock(operation.blockId);
      if (blockState?.content?.documentId) {
        documentId = blockState.content.documentId;
      }

      if (!documentId) {
        console.warn(`No document ID found for block ${operation.blockId}, skipping`);
        continue;
      }

      if (!groups.has(documentId)) {
        groups.set(documentId, []);
      }
      groups.get(documentId)!.push(operation);
    }

    return groups;
  }

  /**
   * Save blocks for a specific document
   */
  private async saveDocumentBlocks(documentId: string, operations: SaveOperation[]) {
    if (!window.fileStorage) {
      throw new Error('File storage not available');
    }

    const store = useDocumentStore.getState();

    // Process operations in batches
    for (let i = 0; i < operations.length; i += this.options.maxBatchSize) {
      const batch = operations.slice(i, i + this.options.maxBatchSize);

      try {
        // Convert to the format expected by the IPC handler
        const blockSaveOperations = batch.map(op => ({
          blockId: op.blockId,
          content: op.content,
          operation: op.operation
        }));

        const result = await window.fileStorage.saveDocumentBlocks(documentId, blockSaveOperations);

        if (result.success) {
          // Mark successful blocks as clean
          result.savedBlocks.forEach(blockId => {
            store.markBlockClean(blockId);
          });

          // Handle failed blocks within the batch
          if (result.failedBlocks.length > 0) {
            result.failedBlocks.forEach(failed => {
              const operation = this.pendingSaveOperations.get(failed.blockId);
              if (operation) {
                operation.retryCount += 1;
                operation.lastAttempt = Date.now();
                this.failedOperations.set(failed.blockId, operation);
              }
            });
          }
        } else {
          // Entire batch failed, mark all for retry
          batch.forEach(op => {
            op.retryCount += 1;
            op.lastAttempt = Date.now();
            this.failedOperations.set(op.blockId, op);
          });
        }

      } catch (error) {
        console.error(`Error saving batch for document ${documentId}:`, error);

        // Mark all operations in this batch for retry
        batch.forEach(op => {
          op.retryCount += 1;
          op.lastAttempt = Date.now();
          this.failedOperations.set(op.blockId, op);
        });
      }
    }

    // Update document metadata if needed
    try {
      await this.updateDocumentMetadata(documentId);
    } catch (error) {
      console.warn(`Failed to update metadata for document ${documentId}:`, error);
    }
  }

  /**
   * Update document metadata with current block information
   */
  private async updateDocumentMetadata(documentId: string) {
    if (!window.fileStorage) return;

    const store = useDocumentStore.getState();
    const existingMetadata = store.documentsMetadata.get(documentId);

    // Use authoritative blockOrder from store metadata - this is set by EditorView
    if (!existingMetadata) {
      console.warn(`[BlockAutoSave] No metadata found for document ${documentId}, skipping metadata update`);
      return;
    }

    // The blockOrder in store metadata is authoritative - set by EditorView from editor.document
    const blockOrder = existingMetadata.blockOrder;
    console.log(`[BlockAutoSave] Using authoritative blockOrder from store metadata for ${documentId}:`, blockOrder);

    const metadata = {
      version: existingMetadata.version || '1.0',
      documentId,
      blockOrder, // Use the authoritative blockOrder from store
      lastModified: new Date().toISOString(),
      totalBlocks: blockOrder.length
    };

    console.log(`[BlockAutoSave] Updating metadata for document ${documentId}:`, metadata);

    try {
      await window.fileStorage.saveDocumentBlocksMetadata(documentId, metadata);

      // Skip store metadata update during save operations to prevent triggering editor reloads
      // The metadata will be refreshed when the document is loaded next time
      console.log(`[BlockAutoSave] Successfully updated file metadata for document ${documentId} (skipped store update)`);
    } catch (error) {
      console.error(`Error updating metadata for document ${documentId}:`, error);
    }
  }

  /**
   * Handle save failures and prepare for retry
   */
  private handleSaveFailure(error: any) {
    console.error('Auto-save failed:', error);

    // Move all pending operations to failed operations for retry
    for (const [blockId, operation] of this.pendingSaveOperations) {
      operation.retryCount += 1;
      operation.lastAttempt = Date.now();
      this.failedOperations.set(blockId, operation);
    }
  }

  /**
   * Schedule retry for failed operations
   */
  private scheduleRetry() {
    if (this.failedOperations.size === 0) return;

    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }

    this.retryTimeout = setTimeout(() => {
      this.retryFailedOperations();
    }, this.options.retryDelayMs);
  }

  /**
   * Retry failed operations that haven't exceeded max retries
   */
  private async retryFailedOperations() {
    const operationsToRetry = new Map<string, SaveOperation>();
    const operationsToAbandon = new Map<string, SaveOperation>();

    // Categorize failed operations
    for (const [blockId, operation] of this.failedOperations) {
      if (operation.retryCount < this.options.maxRetries) {
        operationsToRetry.set(blockId, operation);
      } else {
        operationsToAbandon.set(blockId, operation);
      }
    }

    // Log abandoned operations
    if (operationsToAbandon.size > 0) {
      console.warn(`Abandoning ${operationsToAbandon.size} failed save operations after max retries:`,
        Array.from(operationsToAbandon.keys()));
    }

    // Clear failed operations
    this.failedOperations.clear();

    // Retry salvageable operations
    if (operationsToRetry.size > 0) {
      console.log(`Retrying ${operationsToRetry.size} failed save operations`);
      this.pendingSaveOperations = operationsToRetry;
      await this.processPendingSaves();
    }
  }

  /**
   * Force immediate save of all pending operations
   */
  async forceSave(): Promise<void> {
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
      this.saveTimeout = null;
    }

    await this.processPendingSaves();

    // Also retry failed operations immediately
    if (this.failedOperations.size > 0) {
      this.pendingSaveOperations = new Map(this.failedOperations);
      this.failedOperations.clear();
      await this.processPendingSaves();
    }
  }

  /**
   * Get the current status of the auto-save system
   */
  getStatus() {
    return {
      isProcessing: this.isProcessing,
      pendingOperations: this.pendingSaveOperations.size,
      failedOperations: this.failedOperations.size,
      currentDocument: this.currentDocumentId,
      lastSaveTime: useDocumentStore.getState().lastAutoSaveTime
    };
  }

  /**
   * Clean up resources
   */
  destroy() {
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
      this.saveTimeout = null;
    }

    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
      this.retryTimeout = null;
    }

    this.pendingSaveOperations.clear();
    this.failedOperations.clear();
  }

  /**
   * Pause auto-saving (useful during intensive operations)
   */
  pause() {
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
      this.saveTimeout = null;
    }
  }

  /**
   * Resume auto-saving
   */
  resume() {
    if (this.pendingSaveOperations.size > 0) {
      this.saveTimeout = setTimeout(() => {
        this.processPendingSaves();
      }, this.options.debounceMs);
    }
  }
}

// Export a singleton instance
export const blockAutoSaveManager = new BlockAutoSaveManager();

// Simplified block save operation that works with the consolidated store
export async function performBlockSave(
  docId: string,
  blockOperations: BlockSaveOperation[]
): Promise<BlockBatchSaveResult> {
  console.log(`[BlockAutoSave] Starting save for ${blockOperations.length} blocks in document ${docId}`);

  try {
    // Use the IPC handler to save blocks
    const result = await window.fileStorage.saveDocumentBlocks(docId, blockOperations);

    console.log(`[BlockAutoSave] Completed save for document ${docId}: ${result.success ? 'success' : 'failed'}`);
    return result;

  } catch (error: any) {
    console.error(`[BlockAutoSave] Save failed for document ${docId}:`, error);
    throw error;
  }
}



// Debounced save function for real-time editing
export class DebouncedBlockSaver {
  private timeouts = new Map<string, NodeJS.Timeout>();
  private config: Required<BlockAutoSaveConfig>;

  constructor(config: Partial<BlockAutoSaveConfig> = {}) {
    this.config = {
      debounceMs: 500,
      retryAttempts: 3,
      retryDelayMs: 1000,
      batchSize: 10,
      maxConcurrentSaves: 3,
      ...config
    };
  }

  scheduleSave(docId: string, blockOperations: BlockSaveOperation[]): void {
    const key = `${docId}`;

    // Clear existing timeout
    if (this.timeouts.has(key)) {
      clearTimeout(this.timeouts.get(key)!);
    }

    // Schedule new save
    const timeout = setTimeout(async () => {
      try {
        await performBlockSave(docId, blockOperations);
      } catch (error) {
        console.error(`[DebouncedBlockSaver] Failed to save blocks for document ${docId}:`, error);
      } finally {
        this.timeouts.delete(key);
      }
    }, this.config.debounceMs);

    this.timeouts.set(key, timeout);
  }

  cancelSave(docId: string): void {
    const key = `${docId}`;
    if (this.timeouts.has(key)) {
      clearTimeout(this.timeouts.get(key)!);
      this.timeouts.delete(key);
    }
  }

  cancelAllSaves(): void {
    this.timeouts.forEach(timeout => clearTimeout(timeout));
    this.timeouts.clear();
  }

  hasPendingSave(docId: string): boolean {
    return this.timeouts.has(`${docId}`);
  }
}

// Export the debounced saver as default
export default DebouncedBlockSaver;
