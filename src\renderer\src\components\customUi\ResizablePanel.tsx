import React, { useRef, useState, useEffect } from 'react';
import { cn } from '@renderer/lib/utils';

interface ResizablePanelProps {
  leftPanel: React.ReactNode;
  rightPanel: React.ReactNode;
  initialWidthPercentage?: number;
  minWidthPercentage?: number;
  maxWidthPercentage?: number;
  className?: string;
}

export const ResizablePanel: React.FC<ResizablePanelProps> = ({
  leftPanel,
  rightPanel,
  initialWidthPercentage = 50,
  minWidthPercentage = 20,
  maxWidthPercentage = 80,
  className
}) => {
  const [leftWidth, setLeftWidth] = useState<number>(initialWidthPercentage);
  const containerRef = useRef<HTMLDivElement>(null);
  const isDragging = useRef<boolean>(false);
  const startX = useRef<number>(0);
  const startLeftWidth = useRef<number>(initialWidthPercentage);

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    isDragging.current = true;
    startX.current = e.clientX;
    startLeftWidth.current = leftWidth;
    document.body.style.cursor = 'ew-resize';
    document.body.style.userSelect = 'none';
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging.current || !containerRef.current) return;

      const containerRect = containerRef.current.getBoundingClientRect();
      const containerWidth = containerRect.width;
      const deltaX = e.clientX - startX.current;
      const deltaPercentage = (deltaX / containerWidth) * 100;
      const newLeftWidth = Math.min(
        Math.max(startLeftWidth.current + deltaPercentage, minWidthPercentage),
        maxWidthPercentage
      );

      setLeftWidth(newLeftWidth);
    };

    const handleMouseUp = () => {
      if (!isDragging.current) return;

      isDragging.current = false;
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [minWidthPercentage, maxWidthPercentage]);

  return (
    <div
      ref={containerRef}
      className={cn("flex flex-row h-full", className)}
    >
      <div
        className="h-full overflow-auto"
        style={{ width: `${leftWidth}%` }}
      >
        {leftPanel}
      </div>

      <div
        className="w-1 bg-border hover:bg-primary/50 cursor-ew-resize h-full flex-shrink-0"
        onMouseDown={handleMouseDown}
      />

      <div
        className="h-full overflow-auto"
        style={{ width: `${100 - leftWidth}%` }}
      >
        {rightPanel}
      </div>
    </div>
  );
};
