import React, { useState, useRef, useCallback } from 'react';
import {
  ChevronRight,
  ChevronDown,
  Folder,
  FileText,
  FilePlus2,
  FolderPlus,
  MoreVertical,
  Trash,
  PencilIcon,
  BookmarkIcon,
  GripVertical
} from 'lucide-react';
import { NodeItemProps } from '../types';
import { cn } from '../utils';
import { FileTypeIcon } from './FileTypeIcon';
import { Dropdown } from './Dropdown';

// NodeItem component for rendering each file/folder - Extracted and memoized
export const NodeItem = React.memo(({
  node,
  level,
  currentDocId,
  bookmarkedFiles,
  onFileOpen,
  onToggleExpansion,
  onCreateFile,
  onCreateFolder,
  onRename,
  onDelete,
  onToggleBookmark,
  onItemMove
}: NodeItemProps) => {
  const isExpanded = node.isExpanded !== undefined ? node.isExpanded : false; // Default to not expanded
  const hasChildren = node.children && node.children.length > 0;
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(node.name);
  const inputRef = useRef<HTMLInputElement>(null);
  const [isContextMenuOpen, setIsContextMenuOpen] = useState(false);

  // Handle rename submission for inline editing
  const handleRenameSubmit = async () => {
    if (editName.trim() === '' || editName.trim() === node.name) {
      setIsEditing(false);
      setEditName(node.name); // Revert if empty or unchanged
      return;
    }

    const newName = editName.trim();
    setEditName(newName);
    setIsEditing(false);

    // Trigger the rename callback
    onRename({ ...node, name: newName });
  };

  // Handle drag start
  const handleDragStart = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.dataTransfer.setData('text/plain', node.id);
  }, [node.id]);

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    // Allow drop only on folders
    if (node.type === 'folder') {
      event.preventDefault();
      event.dataTransfer.dropEffect = 'move';
    }
  }, [node.type]);

  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const itemId = event.dataTransfer.getData('text/plain');
    if (itemId && node.type === 'folder' && onItemMove && itemId !== node.id) {
       // Prevent dropping a folder onto itself
      onItemMove(itemId, node.id); // Move item to this folder (parentId: node.id)
    }
  }, [node.id, node.type, onItemMove]);

  // Handle opening a file
  const handleOpenFile = useCallback(() => {
    if (node.type === 'file') {
      onFileOpen(node.id);
    }
  }, [node.id, node.type, onFileOpen]);

  return (
    <>
      <div
        className={cn(
          'group flex items-center justify-between py-1 pr-2 rounded-md text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground',
          currentDocId && node.id === currentDocId && node.type === 'file' ? "bg-primary/10 text-primary" : ""
        )}
        draggable={true}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={() => {
          if (node.type === 'file') {
            handleOpenFile();
          } else {
            onToggleExpansion(node.id);
          }
        }}
        onContextMenu={(e) => {
          e.preventDefault();
          setIsContextMenuOpen(true);
        }}
      >
        {/* Indent and Expander Icon */}
        <div className="flex items-center flex-grow">
          {/* Drag Handle Icon */}
          <GripVertical className="h-4 w-4 mr-1 text-gray-400 dark:text-gray-600 cursor-grab group-hover:opacity-100 opacity-0 transition-opacity" />

          <div style={{ paddingLeft: `${level * 16}px` }} className="flex items-center flex-grow min-w-0">
            {/* Expander Slot - Updated Logic */}
            {node.type === 'folder' ? (
              <span
                onClick={hasChildren ? (e) => {
                  e.stopPropagation();
                  onToggleExpansion(node.id);
                } : undefined} // Clickable only if it has children
                className={`flex-shrink-0 cursor-${hasChildren ? 'pointer' : 'default'} mr-1`}
              >
                {(node.isExpanded && hasChildren) ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
              </span>
            ) : (
              // Placeholder for files to align with folder expander icons (w-4 icon + mr-1 margin = w-5 total space)
              <span className="w-5 flex-shrink-0" />
            )}

            {/* Item Icon */}
            <div className="mr-2 flex-shrink-0">
              {node.type === 'folder' ? (
                <Folder size={16} className="text-blue-500 dark:text-blue-400" />
              ) : (
                <FileTypeIcon fileName={node.name} />
              )}
            </div>

            {/* Item Name or Input */}
            {isEditing ? (
              <input
                type="text"
                value={editName}
                onChange={(e) => setEditName(e.target.value)}
                ref={inputRef}
                className="w-full px-1 py-0.5 border border-input rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary bg-background text-sm min-w-0"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleRenameSubmit();
                  if (e.key === 'Escape') {
                    setIsEditing(false);
                    setEditName(node.name);
                  }
                }}
                onBlur={handleRenameSubmit}
              />
            ) : (
              <span className="truncate select-none text-sm min-w-0">
                {node.name}
              </span>
            )}
          </div>
        </div>

        <div className="ml-1 flex-shrink-0">
          <Dropdown
            isOpen={isContextMenuOpen}
            setIsOpen={setIsContextMenuOpen}
            trigger={
              <button className="h-6 w-6 flex items-center justify-center hover:bg-muted rounded-md">
                <MoreVertical className="h-3.5 w-3.5" />
              </button>
            }
          >
            {node.type === 'file' && (
              <button
                className="flex items-center gap-2 px-3 py-2 w-full text-left hover:bg-accent hover:text-accent-foreground"
                onClick={() => {
                  setIsContextMenuOpen(false);
                  handleOpenFile();
                }}
              >
                <FileText className="h-4 w-4" /> Open Document
              </button>
            )}

            {node.type === 'folder' && (
              <>
                <button
                  className="flex items-center gap-2 px-3 py-2 w-full text-left hover:bg-accent hover:text-accent-foreground"
                  onClick={() => {
                    setIsContextMenuOpen(false);
                    onCreateFile(node.id);
                  }}
                >
                  <FilePlus2 className="h-4 w-4" /> New Document
                </button>

                <button
                  className="flex items-center gap-2 px-3 py-2 w-full text-left hover:bg-accent hover:text-accent-foreground"
                  onClick={() => {
                    setIsContextMenuOpen(false);
                    onCreateFolder(node.id);
                  }}
                >
                  <FolderPlus className="h-4 w-4" /> New Folder
                </button>

                <hr className="my-1 border-border" />
              </>
            )}

            <button
              className="flex items-center gap-2 px-3 py-2 w-full text-left hover:bg-accent hover:text-accent-foreground"
              onClick={() => {
                setIsContextMenuOpen(false);
                onRename(node);
              }}
            >
              <PencilIcon className="h-4 w-4" /> Rename
            </button>

            <button
              className="flex items-center gap-2 px-3 py-2 w-full text-left hover:bg-accent hover:text-accent-foreground"
              onClick={() => {
                setIsContextMenuOpen(false);
                onToggleBookmark(node);
              }}
            >
              <BookmarkIcon className={cn("h-4 w-4", bookmarkedFiles.some(f => f.id === node.id) ? "text-yellow-400 fill-yellow-400" : "")} />
              {bookmarkedFiles.some(f => f.id === node.id) ? 'Unbookmark' : 'Bookmark'}
            </button>

            <hr className="my-1 border-gray-200 dark:border-gray-700" />

            <button
              className="flex items-center gap-2 px-3 py-2 w-full text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-red-500"
              onClick={() => {
                setIsContextMenuOpen(false);
                onDelete(node);
              }}
            >
              <Trash className="h-4 w-4" /> Delete
            </button>
          </Dropdown>
        </div>
      </div>

      {/* Render children if node is a folder and is expanded */}
      {node.type === 'folder' && node.isExpanded && node.children && node.children.length > 0 && (
        <div className="w-full">
          {node.children.map(childNode => (
            <NodeItem
              key={childNode.id}
              node={childNode}
              level={level + 1}
              currentDocId={currentDocId}
              bookmarkedFiles={bookmarkedFiles}
              onFileOpen={onFileOpen}
              onToggleExpansion={onToggleExpansion}
              onCreateFile={onCreateFile}
              onCreateFolder={onCreateFolder}
              onRename={onRename}
              onDelete={onDelete}
              onToggleBookmark={onToggleBookmark}
              onItemMove={onItemMove}
            />
          ))}
        </div>
      )}
    </>
  );
});

NodeItem.displayName = 'NodeItem';
