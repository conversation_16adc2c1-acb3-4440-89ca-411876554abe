import React, { useState, useEffect } from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/ui/dropdown-menu';
import { BlockNoteEditor } from '@blocknote/core';
import {
  Save,
  Loader2,
  CheckCircle2,
  AlertCircle,
  Bold,
  Italic,
  Underline,
  Strikethrough,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  ChevronDown,
  List,
  ListOrdered,
  Indent,
  Outdent,
  Undo,
  Redo,
  Download,
  Wand2,
  Pilcrow
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/ui/button';
import { Separator } from '@/ui/separator';

// Define SaveStatus type locally since we removed the old auto-save system
type SaveStatus = 'idle' | 'saving' | 'saved' | 'error';
type EditorMode = 'edit' | 'preview' | 'split';

interface EditorToolbarProps {
  editor: BlockNoteEditor | null;
  mode: EditorMode;
  hasUnsavedChanges: boolean;
  saveStatus: SaveStatus;
  onModeChange: (mode: EditorMode) => void;
  onSave: () => void;
}

export const EditorToolbar: React.FC<EditorToolbarProps> = ({
  editor,
  mode,
  hasUnsavedChanges,
  saveStatus,
  onSave
}) => {
  if (!editor) return null;

  const [paragraphGap, setParagraphGap] = useState<number>(() => {
    const savedGap = typeof window !== 'undefined' ? localStorage.getItem('editorParagraphGap') : null;
    return savedGap ? parseFloat(savedGap) : 1; // Default to 1 if not found or in SSR
  });
  const paragraphGapOptions = [1, 1.25, 1.5, 1.75, 2];

  const handleParagraphGapChange = (displayedValue: number) => {
    setParagraphGap(displayedValue);
    if (typeof window !== 'undefined') {
      localStorage.setItem('editorParagraphGap', displayedValue.toString());
    }
    if (editor) {
      editor.focus();
    }
  };

  useEffect(() => {
    const actualRemValue = paragraphGap - 0.75;
    // Ensure the calculated value is not negative, though current options prevent this.
    const finalRemValue = Math.max(0, actualRemValue);

    if (typeof window !== 'undefined') {
      document.documentElement.style.setProperty('--paragraph-vertical-gap', `${finalRemValue}rem`);
      console.log(`[EditorToolbar] Applied paragraph gap. Displayed: ${paragraphGap}, Actual REM: ${finalRemValue}rem`);
      const currentGapStyle = document.documentElement.style.getPropertyValue('--paragraph-vertical-gap');
      console.log('[EditorToolbar] CSS variable --paragraph-vertical-gap is now:', currentGapStyle);
    }
  }, [paragraphGap]);


  // Determine if we're in edit or split mode (where formatting is available)
  const isFormattingAvailable = mode === 'edit' || mode === 'split';

  const handleFormatToggle = (format: 'bold' | 'italic' | 'underline' | 'strike') => {
    if (!editor) return;
    try {
      editor.focus();
      if (format === 'bold') {
        editor.toggleStyles({ bold: true });
      } else if (format === 'italic') {
        editor.toggleStyles({ italic: true });
      } else if (format === 'underline') {
        editor.toggleStyles({ underline: true });
      } else if (format === 'strike') {
        editor.toggleStyles({ strike: true });
      }
    } catch (error) {
      console.error('Error toggling format:', error);
    }
  };

  const handleHeadingToggle = (level: 1 | 2 | 3) => {
    if (!editor) return;
    try {
      editor.focus();
      const currentBlock = editor.getTextCursorPosition().block;
      editor.updateBlock(currentBlock, { type: "heading", props: { level } });
    } catch (error) {
      console.error('Error setting heading:', error);
    }
  };

  const handleListToggle = (listType: 'bullet' | 'numbered') => {
    if (!editor) return;
    try {
      editor.focus();
      const currentBlock = editor.getTextCursorPosition().block;
      const blockType = listType === 'bullet' ? "bulletListItem" : "numberedListItem";
      editor.updateBlock(currentBlock, { type: blockType });
    } catch (error) {
      console.error('Error setting list:', error);
    }
  };







  const handleAlignment = (alignment: 'left' | 'center' | 'right' | 'justify') => {
    if (!editor) return;
    try {
      editor.focus();
      const currentBlock = editor.getTextCursorPosition().block;
      editor.updateBlock(currentBlock, {
        props: { textAlignment: alignment }
      });
    } catch (error) {
      console.error('Error setting alignment:', error);
    }
  };

  const handleIndent = () => {
    if (!editor) return;
    try {
      editor.focus();
      editor.nestBlock();
    } catch (error) {
      console.error('Error indenting block:', error);
    }
  };

  const handleOutdent = () => {
    if (!editor) return;
    try {
      editor.focus();
      editor.unnestBlock();
    } catch (error) {
      console.error('Error outdenting block:', error);
    }
  };

  const handleUndo = () => {
    if (!editor) return;
    try {
      editor.focus();
      editor.undo();
    } catch (error) {
      console.error('Error undoing:', error);
    }
  };

  const handleRedo = () => {
    if (!editor) return;
    try {
      editor.focus();
      editor.redo();
    } catch (error) {
      console.error('Error redoing:', error);
    }
  };

  const handleRestyle = () => {
    console.log('Restyle clicked');
  };



  const handleExport = async (format: 'txt' | 'md') => {
    if (!editor) return;
    try {
      // First save the document to ensure latest content is saved
      onSave();

      // Convert blocks to the requested format using BlockNote's native APIs
      let exportContent = '';

      switch (format) {
        case 'txt':
          // For TXT, we'll use our custom function since BlockNote doesn't have a native text export
          exportContent = convertBlocksToText(editor.document);
          break;
        case 'md':
          // Use BlockNote's native markdown export - this handles hyperlinks and formatting properly!
          exportContent = await editor.blocksToMarkdownLossy(editor.document);
          break;
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }

      // Create and download the file
      downloadFile(exportContent, `document.${format}`, format);

    } catch (error) {
      console.error('Error exporting:', error);
    }
  };

  // Helper function to convert blocks to plain text
  const convertBlocksToText = (blocks: any[]): string => {
    return blocks.map(block => {
      if (block.content && Array.isArray(block.content)) {
        return block.content.map((item: any) => item.text || '').join('');
      }
      return '';
    }).join('\n\n');
  };



  // Helper function to download file
  const downloadFile = (content: string, filename: string, format: string) => {
    const blob = new Blob([content], {
      type: format === 'md' ? 'text/markdown' : 'text/plain'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="flex items-center justify-between px-2 pt-4 pb-2 border-b border-border bg-background sticky top-0 z-[100] flex-wrap gap-2">
      <div className="flex items-center space-x-1">
        {/* Undo/Redo - Always available */}
        <div className="flex items-center gap-1">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={handleUndo}
                >
                  <Undo className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" style={{zIndex: 9999}}>Undo (Ctrl+Z)</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={handleRedo}
                >
                  <Redo className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" style={{zIndex: 9999}}>Redo (Ctrl+Y)</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Separator orientation="vertical" className="h-6 mx-1" />
        </div>

        {/* Formatting Options - Only shown in edit or split mode */}
        <div className={cn("flex items-center gap-1", !isFormattingAvailable && "opacity-50 pointer-events-none")}>
          {/* Text Type Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2">
                Text Type
                <ChevronDown className="h-3 w-3 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent style={{zIndex: 9999}}>
              <DropdownMenuItem onClick={() => handleHeadingToggle(1)}>
                Heading 1
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleHeadingToggle(2)}>
                Heading 2
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleHeadingToggle(3)}>
                Heading 3
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => {
                const currentBlock = editor?.getTextCursorPosition().block;
                if (currentBlock) editor?.updateBlock(currentBlock, { type: "paragraph" });
              }}>
                Paragraph
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Restyle Button */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={handleRestyle}
                  disabled={!isFormattingAvailable}
                >
                  <Wand2 className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" style={{zIndex: 9999}}>Restyle</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Basic Formatting */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => handleFormatToggle('bold')}
                  disabled={!isFormattingAvailable}
                >
                  <Bold className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" style={{zIndex: 9999}}>Bold (Ctrl+B)</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => handleFormatToggle('italic')}
                  disabled={!isFormattingAvailable}
                >
                  <Italic className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" style={{zIndex: 9999}}>Italic (Ctrl+I)</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => handleFormatToggle('underline')}
                  disabled={!isFormattingAvailable}
                >
                  <Underline className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" style={{zIndex: 9999}}>Underline (Ctrl+U)</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => handleFormatToggle('strike')}
                  disabled={!isFormattingAvailable}
                >
                  <Strikethrough className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" style={{zIndex: 9999}}>Strikethrough</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Alignment Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2" disabled={!isFormattingAvailable}>
                <AlignLeft className="h-4 w-4" />
                <ChevronDown className="h-3 w-3 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent style={{zIndex: 9999}}>
              <DropdownMenuItem onClick={() => handleAlignment('left')}>
                <AlignLeft className="h-4 w-4 mr-2" />
                Align Left
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleAlignment('center')}>
                <AlignCenter className="h-4 w-4 mr-2" />
                Align Center
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleAlignment('right')}>
                <AlignRight className="h-4 w-4 mr-2" />
                Align Right
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleAlignment('justify')}>
                <AlignJustify className="h-4 w-4 mr-2" />
                Justify
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Paragraph Spacing Dropdown */}
          <DropdownMenu>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-8 w-8" disabled={!isFormattingAvailable} aria-label="Paragraph spacing">
                      <Pilcrow className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                </TooltipTrigger>
                <TooltipContent side="bottom" style={{zIndex: 9999}}>
                  <p>Paragraph Spacing ({paragraphGap})</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <DropdownMenuContent align="start" style={{zIndex: 9999}}>
              {paragraphGapOptions.map((gap) => (
                <DropdownMenuItem
                  key={gap}
                  onClick={() => handleParagraphGapChange(gap)}
                  className={cn('cursor-pointer', paragraphGap === gap ? 'bg-accent text-accent-foreground' : '')}
                >
                  {gap}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* List Options */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2" disabled={!isFormattingAvailable}>
                <List className="h-4 w-4" />
                <ChevronDown className="h-3 w-3 ml-1" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent style={{zIndex: 9999}}>
              <DropdownMenuItem onClick={() => handleListToggle('bullet')}>
                <List className="h-4 w-4 mr-2" />
                Bullet List
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleListToggle('numbered')}>
                <ListOrdered className="h-4 w-4 mr-2" />
                Numbered List
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Link */}
          {/* <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={handleCreateLink}
                  disabled={!isFormattingAvailable}
                >
                  <Link className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" style={{zIndex: 9999}}>Create Link</TooltipContent>
            </Tooltip>
          </TooltipProvider> */}

          {/* <Separator orientation="vertical" className="h-6 mx-1" /> */}

          {/* Indent Controls */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={handleIndent}
                  disabled={!isFormattingAvailable}
                >
                  <Indent className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" style={{zIndex: 9999}}>Indent</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={handleOutdent}
                  disabled={!isFormattingAvailable}
                >
                  <Outdent className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" style={{zIndex: 9999}}>Outdent</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Export Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2 text-xs">
                <Download className="h-3 w-3 mr-1" />
                Export
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent style={{zIndex: 9999}}>
              <DropdownMenuItem onClick={() => handleExport('txt')}>
                Export as TXT
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport('md')}>
                Export as Markdown
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Right side - Save */}
      <div className="flex items-center space-x-2">
        {saveStatus === 'error' && (
          <span className="text-destructive flex items-center text-sm">
            <AlertCircle className="h-3.5 w-3.5 mr-1" />
            Error saving
          </span>
        )}

        {hasUnsavedChanges && saveStatus !== 'error' && (
          <span className="text-amber-500 dark:text-amber-400 text-xs">
            Unsaved changes
          </span>
        )}

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={onSave}
                disabled={saveStatus === 'saving' || !hasUnsavedChanges}
                className={cn(
                  "h-8 w-8",
                  hasUnsavedChanges && saveStatus !== 'saving' && "text-primary",
                  saveStatus === 'saved' && "text-green-500"
                )}
                aria-label="Save document"
              >
                {saveStatus === 'saving' ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : saveStatus === 'saved' ? (
                  <CheckCircle2 className="h-4 w-4" />
                ) : (
                  <Save className="h-4 w-4" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent side="bottom" style={{zIndex: 9999}}>
              <p>
                {saveStatus === 'saving'
                  ? 'Saving...'
                  : hasUnsavedChanges
                    ? 'Save (Ctrl+S)'
                    : 'No changes to save'}
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
};
