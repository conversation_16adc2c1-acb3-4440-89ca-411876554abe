// src/hooks/useTheme.ts
import { useContext } from 'react';
import { ThemeContext, ThemeMode } from '../contexts/ThemeContext';

export interface UseThemeReturn {
  theme: ThemeMode;
  isDarkMode: boolean;
  logoColors: {
    fill: string;
    stroke: string;
    eyes: string;
  };
  toggleTheme: () => void;
  setTheme: (theme: ThemeMode) => void;
}

export const useTheme = (): UseThemeReturn => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
