import { app, dialog, BrowserWindow } from 'electron';
import { autoUpdater } from 'electron-updater';
import { is } from '@electron-toolkit/utils';

class AutoUpdaterManager {
  private isInitialized = false;

  init(): void {
    if (this.isInitialized || is.dev) {
      return; // Don't run auto-updater in development mode
    }

    this.isInitialized = true;
    this.configureAutoUpdater();
    this.setupEventHandlers();

    // Check for updates on startup (with a small delay to ensure app is ready)
    setTimeout(() => {
      this.checkForUpdates();
    }, 3000);
  }

  private configureAutoUpdater(): void {
    // Configure auto-updater for GitHub releases
    // electron-builder with publish.private: true handles the authentication
    autoUpdater.autoDownload = false; // We'll prompt user before downloading
    autoUpdater.allowPrerelease = false; // Only stable releases
    autoUpdater.allowDowngrade = false; // Don't allow downgrades

    // Set the update check interval (check every 4 hours)
    autoUpdater.autoInstallOnAppQuit = true;

    // Configure logging for debugging
    autoUpdater.logger = console;
  }

  private setupEventHandlers(): void {
    // Update checking started
    autoUpdater.on('checking-for-update', () => {
      console.log('Checking for updates...');
    });

    // Update available
    autoUpdater.on('update-available', (info) => {
      console.log('Update available:', info);
      this.showUpdateAvailableDialog(info);
    });

    // No update available
    autoUpdater.on('update-not-available', (info) => {
      console.log('Update not available:', info);
    });

    // Update download progress
    autoUpdater.on('download-progress', (progressObj) => {
      const log = `Download speed: ${progressObj.bytesPerSecond} - Downloaded ${progressObj.percent}% (${progressObj.transferred}/${progressObj.total})`;
      console.log(log);
      this.updateDownloadProgress(progressObj);
    });

    // Update downloaded
    autoUpdater.on('update-downloaded', (info) => {
      console.log('Update downloaded:', info);
      this.showUpdateDownloadedDialog(info);
    });

    // Update error
    autoUpdater.on('error', (err) => {
      console.error('Auto-updater error:', err);
      // Don't show error dialogs to users for update failures
      // Just log them for debugging
    });
  }

  private async showUpdateAvailableDialog(updateInfo: any): Promise<void> {
    const mainWindow = BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];

    if (!mainWindow) return;

    const response = await dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: 'Update Available',
      message: `TruffleNote ${updateInfo.version} is available`,
      detail: `You are currently running version ${app.getVersion()}. Would you like to download the update now?\n\nRelease Notes:\n${updateInfo.releaseNotes || 'No release notes available.'}`,
      buttons: ['Download Update', 'Later'],
      defaultId: 0,
      cancelId: 1
    });

    if (response.response === 0) {
      // User chose to download
      autoUpdater.downloadUpdate();
      this.showDownloadingDialog();
    }
  }

  private showDownloadingDialog(): void {
    const mainWindow = BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];

    if (!mainWindow) return;

    // Show a non-blocking notification that download started
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: 'Downloading Update',
      message: 'Update is being downloaded in the background',
      detail: 'You can continue using TruffleNote. You\'ll be notified when the download is complete.',
      buttons: ['OK']
    });
  }

  private updateDownloadProgress(progressObj: any): void {
    // Could implement a progress bar here in the future
    // For now, just log the progress
    const percent = Math.round(progressObj.percent);
    if (percent % 10 === 0) { // Log every 10%
      console.log(`Update download progress: ${percent}%`);
    }
  }

  private async showUpdateDownloadedDialog(updateInfo: any): Promise<void> {
    const mainWindow = BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];

    if (!mainWindow) return;

    const response = await dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: 'Update Ready',
      message: `TruffleNote ${updateInfo.version} has been downloaded`,
      detail: 'The update is ready to install. TruffleNote will restart to complete the installation.',
      buttons: ['Restart Now', 'Restart Later'],
      defaultId: 0,
      cancelId: 1
    });

    if (response.response === 0) {
      // User chose to restart now
      autoUpdater.quitAndInstall();
    }
    // If user chooses "Restart Later", the update will be installed on next app quit
  }

  // Manual check for updates (can be called from menu or UI)
  checkForUpdates(): void {
    if (is.dev) {
      console.log('Auto-updater disabled in development mode');
      return;
    }

    autoUpdater.checkForUpdatesAndNotify();
  }

  // Get current version
  getCurrentVersion(): string {
    return app.getVersion();
  }
}

// Export singleton instance
export const autoUpdaterManager = new AutoUpdaterManager();
