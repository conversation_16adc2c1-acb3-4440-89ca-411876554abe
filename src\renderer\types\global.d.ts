
// This will replace the existing StoredFile and be used by FileStorageAPI
interface FrontendStoredFile {
  id: string;
  name: string; // For documents or folders, this is the item name
  content?: string; // Content of document.json for 'document' type, otherwise typically undefined for folders
  type: 'document' | 'folder' | 'html' | 'markdown' | 'json' | 'placeholder'; // Added 'document', 'folder'
  createdAt: string;
  updatedAt: string;
  parentId?: string | null; // To establish hierarchy for folders and documents within folders
  documentSettings?: DocumentSettings; // Added per-document settings
}

// New Types for Project Settings
interface SystemPrompt {
  id: string;
  name: string;
  content: string;
  isDefault?: boolean; // Optional: to mark predefined prompts
  scope?: 'global' | 'local'; // Indicates if this is a global or document-specific prompt
  parentPromptId?: string; // Reference to the global prompt this was derived from (for local prompts)
}

interface RestyleConfig {
  id: string;
  name: string;
  apiUrl: string;
  apiKey: string;
  isVisible?: boolean; // Toggle for showing in document dropdown
}

interface ProjectSettings {
  systemPrompts: SystemPrompt[];
  restyleConfigs: RestyleConfig[];
  // Potentially other global project settings can be added here later
}

// New Type for per-document settings
interface DocumentSettings {
  selectedSystemPromptId?: string | null;
  systemPromptContentOverride?: string; // If the user customizes the prompt content for this doc
  isSystemPromptPrepended?: boolean;
  selectedRestyleConfigId?: string | null;
  visibleSystemPromptIds?: string[]; // IDs of global prompts to show in this doc's dropdown
  hyperlinkSavingEnabled?: boolean; // New setting for hyperlink saving toggle
  localSystemPrompts?: SystemPrompt[]; // Document-specific prompts
  // Future per-document settings can be added here
}

// Block operation types
interface BlockSaveOperation {
  blockId: string;
  content: any; // BlockNote Block type
  operation: 'create' | 'update' | 'delete';
}

interface BlockLoadResult {
  blockId: string;
  content: any | null; // BlockNote Block type or null
  success: boolean;
  error?: string;
}

interface BlockBatchSaveResult {
  documentId: string;
  savedBlocks: string[];
  failedBlocks: { blockId: string; error: string }[];
  success: boolean;
}

interface DocumentBlocksMetadata {
  version: string;
  documentId: string;
  blockOrder: string[]; // Array of block IDs in order
  lastModified: string;
  totalBlocks: number;
}

interface FileStorageAPI {
  getStoragePath: () => Promise<string | null>;
  chooseStorageDirectory: () => Promise<string | null>;
  clearStoragePath: () => Promise<boolean>;
  // saveFile: (name: string, dataToSave: { mainContent?: string; documentSettings?: DocumentSettings }, type: 'document' | 'html' | 'markdown' | 'json') => Promise<FrontendStoredFile>;
  getFiles: () => Promise<FrontendStoredFile[]>;
  getFileById: (id: string) => Promise<FrontendStoredFile | null>;
  deleteFile: (id: string) => Promise<{ success: boolean; deletedIds: string[] }>;
  // createFile: (filePathName: string, type: 'document' | 'html' | 'markdown' | 'json', initialContent?: string) => Promise<FrontendStoredFile>; // type can be 'document'
  renameFile: (fileId: string, newName: string) => Promise<{renamedItem: FrontendStoredFile, updatedChildren?: FrontendStoredFile[]}>;
  duplicateFile: (fileIdToDuplicate: string, suggestedNewName: string) => Promise<FrontendStoredFile>;
  createFolder: (folderName: string, parentId?: string | null) => Promise<FrontendStoredFile>; // Added parentId parameter
  exportFile: (fileToExport: FrontendStoredFile) => Promise<boolean>;
  moveItem: (itemId: string, newParentId: string | null) => Promise<{movedItem: FrontendStoredFile, updatedChildren?: FrontendStoredFile[]}>;

  // New IPC calls for project settings
  loadProjectSettings: () => Promise<ProjectSettings>;
  saveProjectSettings: (settings: ProjectSettings) => Promise<void>;

  // New method for creating a structured document
  createDocument: (documentName: string, parentId?: string | null) => Promise<FrontendStoredFile>; // Added parentId

  // New methods for saving document parts
  saveDocumentContent: (docId: string, content: any) => Promise<void>; // content can be any JSON structure
  saveDocumentMetadata: (docId: string, metadata: DocumentSettings) => Promise<void>;

  // Block-based document operations
  saveDocumentBlocks: (
    documentId: string,
    blocks: BlockSaveOperation[]
  ) => Promise<BlockBatchSaveResult>;

  loadDocumentBlocks: (
    documentId: string,
    blockIds?: string[]
  ) => Promise<BlockLoadResult[]>;

  deleteDocumentBlocks: (
    documentId: string,
    blockIds: string[]
  ) => Promise<{ deletedBlocks: string[]; failedBlocks: string[] }>;

  saveDocumentBlocksMetadata: (
    documentId: string,
    metadata: DocumentBlocksMetadata
  ) => Promise<void>;

  loadDocumentBlocksMetadata: (
    documentId: string
  ) => Promise<DocumentBlocksMetadata | null>;

  migrateDocumentToBlocks: (documentId: string) => Promise<boolean>;
  isDocumentBlockBased: (documentId: string) => Promise<boolean>;

  // Saved Paragraphs methods
  getSavedParagraphs: () => Promise<SavedParagraphMetadata[]>;
  getSavedParagraphById: (id: string) => Promise<{ metadata: SavedParagraphMetadata; content: any } | null>;
  saveParagraph: (metadata: Omit<SavedParagraphMetadata, 'id' | 'createdAt' | 'updatedAt'>, content: any) => Promise<SavedParagraphMetadata>;
  updateParagraph: (id: string, metadata: Partial<SavedParagraphMetadata>, content?: any) => Promise<SavedParagraphMetadata>;
  deleteParagraph: (id: string) => Promise<boolean>;

  getSavedParagraphCategories: () => Promise<SavedParagraphCategory[]>;
  saveParagraphCategory: (category: Omit<SavedParagraphCategory, 'id' | 'createdAt' | 'updatedAt'>) => Promise<SavedParagraphCategory>;
  updateParagraphCategory: (id: string, category: Partial<SavedParagraphCategory>) => Promise<SavedParagraphCategory>;
  deleteParagraphCategory: (id: string) => Promise<boolean>;
}

// Saved Paragraphs Feature Interfaces
interface SavedParagraphCategory {
  id: string;
  name: string;
  color?: string; // Optional color for category visualization
  createdAt: string;
  updatedAt: string;
}

interface SavedParagraphMetadata {
  id: string;
  title: string;
  categoryId: string;
  description?: string; // Optional short description
  tags?: string[]; // Optional tags for better organization
  createdAt: string;
  updatedAt: string;
  contentPreview: string; // First ~100 characters for quick preview
  wordCount: number;
  characterCount: number;
}

declare global {
  interface Window {
    // If window.electron is used elsewhere, keep its definition.
    // For now, assuming fileStorage is the primary concern from preload for file-system-storage.ts
    // electron?: {
    //   ipcRenderer: {
    //     invoke: (channel: string, ...args: any[]) => Promise<any>;
    //   };
    // };
    fileStorage: FileStorageAPI;
  }
}

export { FrontendStoredFile, FileStorageAPI, SystemPrompt, RestyleConfig, ProjectSettings, DocumentSettings, BlockSaveOperation, BlockBatchSaveResult, BlockLoadResult, DocumentBlocksMetadata, SavedParagraphCategory, SavedParagraphMetadata };
