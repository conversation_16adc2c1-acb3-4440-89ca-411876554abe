// Constants for copy functionality
export const COPY_CONSTANTS = {
    HTML_TEMPLATE: (content: string) =>
      `<!DOCTYPE html><html><head><meta charset="UTF-8"><title>Copied Content</title></head><body>${content}</body></html>`,
    FALLBACK_HTML: (text: string) => `<p>${text.replace(/\n/g, '<br>')}</p>`,
    ERROR_MESSAGES: {
      NO_EDITOR: 'Copy failed: No document available',
      NO_CONTENT: 'Nothing to copy',
      CLIPBOARD_FAILED: 'Failed to copy content',
      FALLBACK_SUCCESS: 'Content copied as plain text (fallback)',
    }
  } as const;