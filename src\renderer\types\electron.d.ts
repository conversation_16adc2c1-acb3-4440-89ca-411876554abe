export {}; // Ensure this file is treated as a module

interface AppAPI {
  checkForUpdates: () => Promise<boolean>;
  getVersion: () => Promise<string>;
}

interface ElectronAPI {
  ipcRenderer: {
    send: (channel: string, ...args: any[]) => void;
    on: (channel: string, listener: (event: any, ...args: any[]) => void) => () => void;
    off: (channel: string, listener: any) => void;
    invoke: (channel: string, ...args: any[]) => Promise<any>;
    removeAllListeners: (channel: string) => void;
  };
  process: {
    platform: string;
    versions: any;
  };
}

declare global {
  interface Window {
    electron: ElectronAPI;
    fileStorage?: any; // You already have this, added for completeness of example
    app?: AppAPI; // Auto-updater and app management API
    // Add other preload-exposed properties here
  }
}
