import React, { useState, useEffect, useRef } from 'react';
import { useDrag, useDrop, type DropTargetMonitor } from 'react-dnd';
import {
  GripVerticalIcon,
  CheckIcon,
  XIcon,
  Maximize2Icon,
  Trash2Icon,
  LinkIcon,
  UnlinkIcon,
  InfoIcon
} from 'lucide-react';
import { Badge } from '@/ui/badge';
import { Input } from '@/ui/input';
import { Button } from '@/ui/button';
import { Textarea } from '@/ui/textarea';
import { Switch } from '@/ui/switch';
import { cn } from '@/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider
} from '@/ui/tooltip';
import { PromptActionMenu } from '../../PromptActionMenu';
import { useNotification } from '@/contexts/NotificationContext';
import { DraggablePromptItemProps, DND_ITEM_TYPE_PROMPT } from '../types';

export const DraggablePromptItem: React.FC<DraggablePromptItemProps> = ({
  prompt,
  index,
  movePrompt,
  activeDocumentId,
  activeDocumentSettings,
  handleVisibilityToggle,
  handleInitiateDelete,
  handleUpdatePromptContent,
  handleUpdatePromptName,
  onForkToLocal,
  onSaveAsGlobal,
  onReplaceGlobal,
  onResetToGlobal,
  onDetachFromGlobal,
  onConvertToGlobal,
  isLocal = false,
  parentGlobalPrompt = null,
  projectSettingsSystemPrompts,
  setPromptToEdit,
  setExpandedEditorOpen
}) => {
  // Define safe versions of the callback functions
  const handleForkToLocal = onForkToLocal ? () => onForkToLocal() : () => {};
  const handleSaveAsGlobal = onSaveAsGlobal ? () => onSaveAsGlobal() : () => {};
  const handleReplaceGlobal = onReplaceGlobal ? () => onReplaceGlobal() : () => {};
  const handleResetToGlobal = onResetToGlobal ? () => onResetToGlobal() : () => {};
  const handleDetachFromGlobal = onDetachFromGlobal ? () => onDetachFromGlobal() : () => {};
  const handleConvertToGlobal = onConvertToGlobal ? () => onConvertToGlobal() : () => {};

  const itemRef = useRef<HTMLDivElement>(null);
  const handleRef = useRef<HTMLDivElement>(null);

  const [isEditingName, setIsEditingName] = useState(false);
  const [currentNameValue, setCurrentNameValue] = useState(prompt.name);
  const [isContentDirty, setIsContentDirty] = useState(false);
  const [currentContentValue, setCurrentContentValue] = useState(prompt.content);
  const [isTextareaInteractive, setIsTextareaInteractive] = useState(false);
  const { showNotification } = useNotification();

  useEffect(() => {
    if (!isEditingName) {
        setCurrentNameValue(prompt.name);
    }
  }, [prompt.name, isEditingName]);

  useEffect(() => {
    if (!isContentDirty) {
      setCurrentContentValue(prompt.content);
    }
  }, [prompt.content, isContentDirty]);

  useEffect(() => {
    setIsEditingName(false);
    setIsContentDirty(false);
    setIsTextareaInteractive(false);
    setCurrentNameValue(prompt.name);
    setCurrentContentValue(prompt.content);
  }, [prompt.id, prompt.name, prompt.content]);

  const [, drop] = useDrop({
    accept: DND_ITEM_TYPE_PROMPT,
    hover(item: { id: string; index: number }, monitor: DropTargetMonitor) {
      if (!itemRef.current) return;
      const dragIndex = item.index;
      const hoverIndex = index;
      if (dragIndex === hoverIndex) return;
      const hoverBoundingRect = itemRef.current?.getBoundingClientRect();
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      if (!clientOffset) return;
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;
      movePrompt(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag, dragPreview] = useDrag({
    type: DND_ITEM_TYPE_PROMPT,
    item: { id: prompt.id, index },
    collect: (monitor) => ({ isDragging: monitor.isDragging() }),
  });

  drag(handleRef);
  drop(itemRef);
  dragPreview(itemRef);

  const onSaveName = async () => {
    const newNameTrimmed = currentNameValue.trim();
    if (newNameTrimmed === '') {
      showNotification("Prompt name cannot be empty.", "error");
      setCurrentNameValue(prompt.name);
      setIsEditingName(false);
      return;
    }
    if (newNameTrimmed !== prompt.name) {
      await handleUpdatePromptName({ ...prompt, name: newNameTrimmed }, newNameTrimmed);
    }
    setIsEditingName(false);
  };

  const onCancelNameEdit = () => {
    setCurrentNameValue(prompt.name);
    setIsEditingName(false);
  };

  const onSaveContent = async () => {
    const originalPromptFromSettings = projectSettingsSystemPrompts.find(p => p.id === prompt.id);
    const originalContent = originalPromptFromSettings ? originalPromptFromSettings.content : prompt.content;

    if (currentContentValue !== originalContent) {
        await handleUpdatePromptContent({ ...prompt, content: currentContentValue }, currentContentValue);
    }
    setIsContentDirty(false);
  };

  const onCancelContentEdit = () => {
    const originalPrompt = projectSettingsSystemPrompts.find(p => p.id === prompt.id);
    setCurrentContentValue(originalPrompt ? originalPrompt.content : prompt.content);
    setIsContentDirty(false);
    setIsTextareaInteractive(false); // Ensure textarea collapses
  };

  return (
    <div
      ref={itemRef}
      style={{ opacity: isDragging ? 0.5 : 1 }}
      className={cn(
        "max-w-[97%] p-3 border border-border rounded-md flex flex-col group mb-4 relative bg-secondary dark:bg-secondary",
      )}
    >
      {/* Badge for local prompts */}
      {isLocal && parentGlobalPrompt && (
        <div className="mb-1">
          <Badge variant="info" className="text-xs whitespace-nowrap overflow-hidden text-ellipsis max-w-[200px] inline-block">
            Forked from: {parentGlobalPrompt.name}
          </Badge>
        </div>
      )}

      {isLocal && !parentGlobalPrompt && (
        <div className="mb-1">
          <Badge variant="secondary" className="text-xs">
            Document Only
          </Badge>
        </div>
      )}

      <div className="flex items-center justify-between mb-2 gap-2">
        <div className="flex items-center flex-grow min-w-0 gap-1">
          {/* Drag handle */}
          <div
            ref={handleRef}
            className="cursor-grab touch-none flex items-center mr-1"
          >
            <GripVerticalIcon className="h-4 w-4 text-muted-foreground" />
          </div>

          {isEditingName ? (
            <div className="flex-grow flex items-center gap-1">
              <Input
                value={currentNameValue}
                onChange={(e) => setCurrentNameValue(e.target.value)}
                className="h-8 text-sm flex-grow dark:bg-input text-text-foreground"
                autoFocus
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    onSaveName();
                  } else if (e.key === 'Escape') {
                    onCancelNameEdit();
                  }
                }}
              />
              <TooltipProvider delayDuration={300}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" onClick={onSaveName} className="h-7 w-7 group/save">
                      <CheckIcon className="h-4 w-4 text-primary group-hover/save:text-primary-foreground" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom"><p>Save name</p></TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider delayDuration={300}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" onClick={onCancelNameEdit} className="h-7 w-7 group/cancel">
                      <XIcon className="h-4 w-4 text-destructive group-hover/cancel:text-primary-foreground" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom"><p>Cancel</p></TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          ) : (
            <div
              className="flex-grow min-w-0 font-medium text-sm cursor-pointer hover:underline underline-offset-2"
              onClick={() => setIsEditingName(true)}
              title="Click to edit prompt name"
            >
              <span className="truncate block text-foreground">{prompt.name}</span>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2 shrink-0">
          {/* Visibility toggle for both global and document prompts */}
          {activeDocumentId && activeDocumentSettings && (
            <TooltipProvider delayDuration={300}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center">
                    <Switch
                      id={`visibility-${prompt.id}`}
                      checked={activeDocumentSettings.visibleSystemPromptIds?.includes(prompt.id) || false}
                      onCheckedChange={(checked) => handleVisibilityToggle(prompt.id, checked)}
                      className="h-[18px] w-9 custom-switch data-[state=checked]:custom-switch-on"
                    />
                  </div>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Show in document prompt dropdown</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Action Menu */}
          <PromptActionMenu
            prompt={prompt}
            isLocal={isLocal}
            onForkToLocal={handleForkToLocal} // This is the function we're interested in
            onSaveAsGlobal={handleSaveAsGlobal}
            onReplaceGlobal={handleReplaceGlobal}
            onResetToGlobal={handleResetToGlobal}
            onDetachFromGlobal={handleDetachFromGlobal}
            onConvertToGlobal={handleConvertToGlobal}
            hasParentGlobal={!!prompt.parentPromptId}
          />

          <TooltipProvider delayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 group/expand" // Added '/expand' to group
                  onClick={() => {
                    setPromptToEdit(prompt);
                    setExpandedEditorOpen(true);
                  }}
                >
                  <Maximize2Icon className="h-4 w-4 text-muted-foreground group-hover/expand:text-primary-foreground" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom"><p>Expand editor</p></TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider delayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" onClick={() => handleInitiateDelete(prompt)} className="h-7 w-7 group/delete-btn">
                  <Trash2Icon className="h-4 w-4 text-muted-foreground group-hover/delete-btn:text-destructive" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom"><p>Delete prompt</p></TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <div className="relative">
        <Textarea
          value={currentContentValue}
          onFocus={() => setIsTextareaInteractive(true)}
          onBlur={() => setIsTextareaInteractive(false)}
          onChange={(e) => {
            setCurrentContentValue(e.target.value);
            setIsContentDirty(true);
          }}
          placeholder="Enter prompt content..."
          className={cn(
            "text-sm overflow-y-auto focus:ring-1 focus:ring-ring prompt-content-scrollbar bg-input border-border text-text-foreground",
            (isContentDirty || isTextareaInteractive) ? "min-h-[120px] max-h-[600px]" : "h-20"
          )}
        />
      </div>

      {isContentDirty && (
        <div className="flex justify-end gap-2 pt-2">
          <Button variant="outline" size="sm" onClick={onCancelContentEdit} className="h-7 text-xs px-2">
            Cancel
          </Button>
          <Button size="sm" onClick={onSaveContent} className="h-7 text-xs px-2">
            Save Changes
          </Button>
        </div>
      )}
    </div>
  );
};
