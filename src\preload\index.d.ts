// Define the minimal electron API interface
interface ElectronAPI {
  ipcRenderer: {
    send: (channel: string, ...args: any[]) => void;
    on: (channel: string, listener: (event: any, ...args: any[]) => void) => () => void;
    off: (channel: string, listener: any) => void;
    invoke: (channel: string, ...args: any[]) => Promise<any>;
    removeAllListeners: (channel: string) => void;
  };
  process: {
    platform: string;
    versions: any;
  };
}

declare global {
  interface Window {
    electron: ElectronAPI
    api: unknown
  }
}
