import { useCallback } from 'react';
import { nanoid } from 'nanoid';
import { ProjectSettings, DocumentSettings, SystemPrompt } from '@/types/global';
import { useNotification } from '@/contexts/NotificationContext';

interface UsePromptActionsProps {
  editedGlobalPrompts: SystemPrompt[];
  setEditedGlobalPrompts: (prompts: SystemPrompt[]) => void;
  editedLocalPrompts: SystemPrompt[];
  setEditedLocalPrompts: (prompts: SystemPrompt[]) => void;
  projectSettings?: ProjectSettings;
  activeDocumentSettings?: DocumentSettings | null;
  activeDocumentId: string | null;
  onUpdateProjectSettings?: (updatedSettings: ProjectSettings) => Promise<void>;
  onUpdateActiveDocumentSettings?: (updatedSettings: DocumentSettings) => Promise<void>;
  setActiveTab: (tab: string) => void;
}

export const usePromptActions = ({
  editedGlobalPrompts,
  setEditedGlobalPrompts,
  editedLocalPrompts,
  setEditedLocalPrompts,
  projectSettings,
  activeDocumentSettings,
  activeDocumentId,
  onUpdateProjectSettings,
  onUpdateActiveDocumentSettings,
  setActiveTab
}: UsePromptActionsProps) => {
  const { showNotification } = useNotification();

  const handleForkGlobalToLocal = useCallback(async (globalPrompt: SystemPrompt) => {
    if (!activeDocumentId || !activeDocumentSettings) {
      showNotification("No active document selected.", "error");
      return;
    }

    const localCopy: SystemPrompt = {
      id: nanoid(),
      name: globalPrompt.name, // Keep the original name
      content: globalPrompt.content,
      scope: 'local',
      parentPromptId: globalPrompt.id // Link to the original global prompt
    };

    const updatedLocalPrompts = [...editedLocalPrompts, localCopy];
    setEditedLocalPrompts(updatedLocalPrompts);

    try {
      if (onUpdateActiveDocumentSettings) {
        await onUpdateActiveDocumentSettings({
          ...activeDocumentSettings,
          localSystemPrompts: updatedLocalPrompts.map(p => {
            const { scope, ...rest } = p;
            return rest;
          })
        });
        showNotification(`Duplicated global prompt to document: ${localCopy.name}`, "success");
        setActiveTab('document');
      }
    } catch (error) {
      console.error("Error duplicating global prompt:", error);
      showNotification("Failed to duplicate global prompt to document.", "error");
      // Revert local prompts if saving failed
      setEditedLocalPrompts(editedLocalPrompts.filter(p => p.id !== localCopy.id));
    }
  }, [activeDocumentId, activeDocumentSettings, editedLocalPrompts, setEditedLocalPrompts, onUpdateActiveDocumentSettings, showNotification, setActiveTab]);

  const handleSaveLocalAsGlobal = useCallback(async (localPrompt: SystemPrompt) => {
    const globalCopy: SystemPrompt = {
      id: nanoid(),
      name: localPrompt.name,
      content: localPrompt.content,
      scope: 'global'
    };

    const updatedGlobalPrompts = [...editedGlobalPrompts, globalCopy];
    setEditedGlobalPrompts(updatedGlobalPrompts);

    const updatedLocalPrompts = editedLocalPrompts.filter(p => p.id !== localPrompt.id);
    setEditedLocalPrompts(updatedLocalPrompts);

    try {
      if (projectSettings && onUpdateProjectSettings) {
        await onUpdateProjectSettings({
          ...projectSettings,
          systemPrompts: updatedGlobalPrompts.map(p => {
            const { scope, ...rest } = p;
            return rest;
          })
        });

        if (activeDocumentSettings && onUpdateActiveDocumentSettings) {
          await onUpdateActiveDocumentSettings({
            ...activeDocumentSettings,
            localSystemPrompts: updatedLocalPrompts.map(p => {
              const { scope, ...rest } = p;
              return rest;
            })
          });
        }

        showNotification(`Moved to global prompts: ${globalCopy.name}`, "success");
        setActiveTab('global');
      }
    } catch (error) {
      console.error("Error saving local prompt as global:", error);
      showNotification("Failed to save local prompt as global.", "error");
      setEditedGlobalPrompts(editedGlobalPrompts);
      setEditedLocalPrompts(editedLocalPrompts);
    }
  }, [editedGlobalPrompts, setEditedGlobalPrompts, editedLocalPrompts, setEditedLocalPrompts, projectSettings, activeDocumentSettings, onUpdateProjectSettings, onUpdateActiveDocumentSettings, showNotification, setActiveTab]);

  const handleReplaceGlobal = useCallback(async (localPrompt: SystemPrompt) => {
    if (!localPrompt.parentPromptId) {
      showNotification("This local prompt is not linked to a global prompt.", "error");
      return;
    }

    const originalGlobalPrompt = editedGlobalPrompts.find(p => p.id === localPrompt.parentPromptId);
    if (!originalGlobalPrompt) {
      showNotification("Could not find the original global prompt to replace.", "error");
      return;
    }

    const updatedGlobalPrompt = {
      ...originalGlobalPrompt,
      content: localPrompt.content
    };

    const updatedGlobalPrompts = editedGlobalPrompts.map(p =>
      p.id === localPrompt.parentPromptId ? updatedGlobalPrompt : p
    );

    setEditedGlobalPrompts(updatedGlobalPrompts);

    try {
      if (projectSettings && onUpdateProjectSettings) {
        await onUpdateProjectSettings({
          ...projectSettings,
          systemPrompts: updatedGlobalPrompts.map(p => {
            const { scope, ...rest } = p;
            return rest;
          })
        });
        showNotification(`Updated global prompt: ${updatedGlobalPrompt.name}`, "success");
      }
    } catch (error) {
      console.error("Error replacing global prompt:", error);
      showNotification("Failed to replace global prompt.", "error");
      setEditedGlobalPrompts(editedGlobalPrompts);
    }
  }, [editedGlobalPrompts, setEditedGlobalPrompts, projectSettings, onUpdateProjectSettings, showNotification]);

  const handleResetToGlobal = useCallback(async (localPrompt: SystemPrompt) => {
    if (!localPrompt.parentPromptId) {
      showNotification("This local prompt is not linked to a global prompt.", "error");
      return;
    }

    const globalPrompt = editedGlobalPrompts.find(p => p.id === localPrompt.parentPromptId);
    if (!globalPrompt) {
      showNotification("Could not find the original global prompt.", "error");
      return;
    }

    const updatedLocalPrompts = editedLocalPrompts.map(p =>
      p.id === localPrompt.id ? { ...p, content: globalPrompt.content } : p
    );
    setEditedLocalPrompts(updatedLocalPrompts);

    try {
      if (activeDocumentSettings && onUpdateActiveDocumentSettings) {
        await onUpdateActiveDocumentSettings({
          ...activeDocumentSettings,
          localSystemPrompts: updatedLocalPrompts.map(p => {
            const { scope, ...rest } = p;
            return rest;
          })
        });
        showNotification(`Local prompt reset to global: ${localPrompt.name}`, "success");
      }
    } catch (error) {
      console.error("Error resetting local prompt:", error);
      showNotification("Failed to reset local prompt.", "error");
      setEditedLocalPrompts(editedLocalPrompts);
    }
  }, [editedGlobalPrompts, editedLocalPrompts, setEditedLocalPrompts, activeDocumentSettings, onUpdateActiveDocumentSettings, showNotification]);

  const handleDetachFromGlobal = useCallback(async (localPrompt: SystemPrompt) => {
    if (!localPrompt.parentPromptId) {
      showNotification("This local prompt is not linked to a global prompt.", "error");
      return;
    }

    const updatedLocalPrompts = editedLocalPrompts.map(p =>
      p.id === localPrompt.id ? { ...p, parentPromptId: undefined } : p
    );
    setEditedLocalPrompts(updatedLocalPrompts);

    try {
      if (activeDocumentSettings && onUpdateActiveDocumentSettings) {
        await onUpdateActiveDocumentSettings({
          ...activeDocumentSettings,
          localSystemPrompts: updatedLocalPrompts.map(p => {
            const { scope, ...rest } = p;
            return rest;
          })
        });
        showNotification(`Local prompt detached from global: ${localPrompt.name}`, "success");
      }
    } catch (error) {
      console.error("Error detaching local prompt:", error);
      showNotification("Failed to detach local prompt.", "error");
      setEditedLocalPrompts(editedLocalPrompts);
    }
  }, [editedLocalPrompts, setEditedLocalPrompts, activeDocumentSettings, onUpdateActiveDocumentSettings, showNotification]);

  const handleConvertToGlobal = useCallback(async (localPrompt: SystemPrompt) => {
    await handleSaveLocalAsGlobal(localPrompt);
    showNotification(`Converted "${localPrompt.name}" to a global prompt.`, "success");
  }, [handleSaveLocalAsGlobal, showNotification]);

  return {
    handleForkGlobalToLocal,
    handleSaveLocalAsGlobal,
    handleReplaceGlobal,
    handleResetToGlobal,
    handleDetachFromGlobal,
    handleConvertToGlobal
  };
};
