import React, { useCallback } from 'react';
import { RootDropAreaProps } from '../types';

// Root drop area component - Extracted as a separate component
export const RootDropArea = React.memo(({ onItemMove }: RootDropAreaProps) => {
  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault(); // Necessary to allow dropping
    event.dataTransfer.dropEffect = 'move'; // Indicate a move operation
  }, []);

  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const itemId = event.dataTransfer.getData('text/plain');
    if (itemId && onItemMove) {
      onItemMove(itemId, null); // Move to root (parentId: null)
    }
  }, [onItemMove]);

  return (
    <div
      className="mt-2 p-4 border border-dashed border-gray-300 dark:border-gray-700 rounded-md text-center text-gray-500 dark:text-gray-400 hover:border-blue-500 dark:hover:border-blue-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      Drag items here to move to root
    </div>
  );
});

RootDropArea.displayName = 'RootDropArea';
