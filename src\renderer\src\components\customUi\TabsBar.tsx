import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { X, ChevronDown, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { FrontendStoredFile } from '@/types/global';
import { useDrag, useDrop, DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem
} from '@/ui/dropdown-menu';
import { ScrollArea } from '@/ui/scroll-area';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider
} from '@/ui/tooltip';
import { useDocumentStore, useOpenFiles, useActiveFileId } from '@/stores/blockDocumentStore';

// Type for the drag item
interface DragItem {
  index: number;
  id: string;
  type: string;
}

// Constant for drag type
const DRAG_TYPE = 'TAB_ITEM';

// Helper to truncate text - extracted as a pure function
const truncateText = (text: string, maxLength: number = 15) => {
  return text.length > maxLength ? `${text.substring(0, maxLength)}..` : text;
};

// Tab component - extracted and memoized
const Tab = React.memo(({
  file,
  index,
  active,
  moveTab,
  onSelect,
  onClose
}: {
  file: FrontendStoredFile;
  index: number;
  active: boolean;
  moveTab: (dragIndex: number, hoverIndex: number) => void;
  onSelect: (id: string) => void;
  onClose: (id: string) => void;
}) => {
  const ref = useRef<HTMLDivElement>(null);

  // Set up drop target for drag-and-drop reordering
  const [{ handlerId }, drop] = useDrop({
    accept: DRAG_TYPE,
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item: DragItem, monitor) {
      if (!ref.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;

      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return;
      }

      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect();

      // Get horizontal middle
      const hoverMiddleX =
        (hoverBoundingRect.right - hoverBoundingRect.left) / 2;

      // Determine mouse position
      const clientOffset = monitor.getClientOffset();

      // Get pixels to the left
      const hoverClientX = (clientOffset?.x ?? 0) - hoverBoundingRect.left;

      // Only perform the move when the mouse has crossed half of the items width
      if (dragIndex < hoverIndex && hoverClientX < hoverMiddleX) {
        return;
      }
      if (dragIndex > hoverIndex && hoverClientX > hoverMiddleX) {
        return;
      }

      // Time to actually perform the action
      moveTab(dragIndex, hoverIndex);

      // Update the drag item's index
      item.index = hoverIndex;
    },
  });

  // Set up drag source
  const [{ isDragging }, drag] = useDrag({
    type: DRAG_TYPE,
    item: () => {
      return { id: file.id, index };
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const opacity = isDragging ? 0.4 : 1;

  // Connect drag and drop refs
  drag(drop(ref));

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      // Select tab on Enter or Space
      e.preventDefault();
      onSelect(file.id);
    } else if (e.key === 'Delete' || e.key === 'Backspace') {
      // Close tab on Delete or Backspace
      e.preventDefault();
      e.stopPropagation();
      onClose(file.id);
    }
  }, [file.id, onSelect, onClose]);

  return (
    <div
      ref={ref}
      onClick={() => onSelect(file.id)}
      onKeyDown={handleKeyDown}
      data-handler-id={handlerId}
      className={cn(
        'flex items-center justify-between gap-2 px-3 py-1.5 border-r border-border whitespace-nowrap h-full cursor-pointer group transition-colors',
        active
          ? 'bg-background text-accent'
          : 'text-muted-foreground hover:bg-muted/50 hover:text-foreground focus:outline-none focus:ring-1 focus:ring-ring/50'
      )}
      style={{ opacity }}
      title={file.name}
      role="tab"
      aria-selected={active}
      tabIndex={active ? 0 : -1} // Make active tab keyboard focusable
    >
      <span className="truncate max-w-[100px] leading-tight">
        {truncateText(file.name)}
      </span>
      <button
        onClick={(e) => {
          e.stopPropagation();
          onClose(file.id);
        }}
        className={cn(
          'p-0.5 rounded hover:bg-destructive/20 text-muted-foreground hover:text-destructive focus:outline-none focus:ring-1 focus:ring-destructive/50',
          active ? 'opacity-70 hover:opacity-100' : 'opacity-0 group-hover:opacity-60 hover:opacity-100'
        )}
        aria-label={`Close tab: ${file.name}`}
      >
        <X className="w-3.5 h-3.5" />
      </button>
    </div>
  );
});
Tab.displayName = 'Tab'; // For better debugging with React.memo

// Main TabsBar component
const TabsBar = () => {
  // Use selectors for optimized renders
  const openFiles = useOpenFiles();
  const activeFileId = useActiveFileId();
  const { selectTab, closeTab, reorderTabs } = useDocumentStore();

  // Local state
  const [tabs, setTabs] = useState<FrontendStoredFile[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const [visibleTabCount, setVisibleTabCount] = useState(5);
  const [isKeyboardNavigating, setIsKeyboardNavigating] = useState(false);

  // Update tabs when openFiles changes
  useEffect(() => {
    setTabs(openFiles);
  }, [openFiles]);

  // Calculate how many tabs can be shown based on container width
  useEffect(() => {
    const calculateVisibleTabCount = () => {
      if (!containerRef.current) return;
      const containerWidth = containerRef.current.offsetWidth;
      // Average tab width - approximately 120px
      const averageTabWidth = 120;
      // Reserve space for dropdown button
      const dropdownWidth = 30;
      // Note: We've added 40px paddingLeft to the container to account for the
      // collapsed sidebar (32px) + padding (8px)
      const availableSpace = containerWidth - dropdownWidth;
      const calculatedCount = Math.floor(availableSpace / averageTabWidth);
      setVisibleTabCount(Math.max(1, calculatedCount));
    };

    calculateVisibleTabCount();

    const handleResize = () => {
      calculateVisibleTabCount();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Define handlers with useCallback for memoization
  const handleSelectTab = useCallback((fileId: string) => {
    selectTab(fileId);
  }, [selectTab]);

  const handleCloseTab = useCallback((fileId: string) => {
    closeTab(fileId);
  }, [closeTab]);

  const moveTab = useCallback((dragIndex: number, hoverIndex: number) => {
    // Update the local state for immediate visual feedback
    const newTabs = [...tabs];
    const draggedTab = newTabs[dragIndex];
    newTabs.splice(dragIndex, 1);
    newTabs.splice(hoverIndex, 0, draggedTab);
    setTabs(newTabs);

    // Sync the store with the new order
    reorderTabs(newTabs);
  }, [tabs, reorderTabs]);

  // Handle keyboard shortcuts for tab navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.ctrlKey && e.key === 'Tab') {
      e.preventDefault();

      // Find the index of the current active tab
      const activeIndex = tabs.findIndex(tab => tab.id === activeFileId);

      if (activeIndex !== -1) {
        // Determine the next tab (with looping)
        const nextIndex = e.shiftKey
          ? (activeIndex - 1 + tabs.length) % tabs.length // Ctrl+Shift+Tab goes left
          : (activeIndex + 1) % tabs.length; // Ctrl+Tab goes right

        handleSelectTab(tabs[nextIndex].id);
        setIsKeyboardNavigating(true);
      }
    }
  }, [tabs, activeFileId, handleSelectTab]);

  // Disable keyboard navigation indicator when mouse moves
  useEffect(() => {
    const handleMouseMove = () => {
      if (isKeyboardNavigating) {
        setIsKeyboardNavigating(false);
      }
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [isKeyboardNavigating]);

  // Create the memoized visible and hidden tabs arrays
  const { visibleTabs, hiddenTabs, shouldShowDropdown } = useMemo(() => {
    const visibleTabs = tabs.slice(0, visibleTabCount);
    const hiddenTabs = tabs.slice(visibleTabCount);
    const shouldShowDropdown = hiddenTabs.length > 0;

    return { visibleTabs, hiddenTabs, shouldShowDropdown };
  }, [tabs, visibleTabCount]);

  // Don't render if no tabs are open
  if (tabs.length === 0) {
    return null;
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div
        className="flex items-center bg-background border-border h-10 overflow-hidden group/tabsbar"
        style={{ paddingLeft: '40px' }}
        ref={containerRef}
        onKeyDown={handleKeyDown}
        role="tablist"
        aria-label="Document tabs"
      >
        <div className="flex h-full">
          {visibleTabs.map((file, index) => (
            <Tab
              key={file.id}
              file={file}
              index={index}
              active={file.id === activeFileId}
              moveTab={moveTab}
              onSelect={handleSelectTab}
              onClose={handleCloseTab}
            />
          ))}
        </div>

        {shouldShowDropdown && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button
                      className="flex items-center justify-center h-8 w-8 hover:bg-muted rounded-md mx-1"
                      aria-label="Show all tabs"
                    >
                      <ChevronDown className="h-4 w-4 text-muted-foreground" />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56 max-h-[400px]">
                    <ScrollArea className="max-h-[300px]">
                      {hiddenTabs.map((file) => (
                        <DropdownMenuItem
                          key={file.id}
                          onClick={() => handleSelectTab(file.id)}
                          className="flex justify-between items-center"
                        >
                          <span className="truncate flex-1">{file.name}</span>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleCloseTab(file.id);
                            }}
                            className="p-0.5 rounded hover:bg-destructive/20 text-muted-foreground hover:text-destructive ml-2"
                            aria-label={`Close tab: ${file.name}`}
                          >
                            <X className="w-3.5 h-3.5" />
                          </button>
                        </DropdownMenuItem>
                      ))}
                    </ScrollArea>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>Show all tabs</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}

        {/* Tab navigation controls for keyboard users */}
        {isKeyboardNavigating && tabs.length > 1 && (
          <div className="flex items-center ml-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={() => {
                      const activeIndex = tabs.findIndex(tab => tab.id === activeFileId);
                      if (activeIndex !== -1) {
                        const prevIndex = (activeIndex - 1 + tabs.length) % tabs.length;
                        handleSelectTab(tabs[prevIndex].id);
                      }
                    }}
                    className="h-6 w-6 flex items-center justify-center hover:bg-muted rounded-sm"
                    aria-label="Previous tab"
                  >
                    <ChevronLeft className="h-3.5 w-3.5 text-muted-foreground" />
                  </button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Previous tab (Ctrl+Shift+Tab)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={() => {
                      const activeIndex = tabs.findIndex(tab => tab.id === activeFileId);
                      if (activeIndex !== -1) {
                        const nextIndex = (activeIndex + 1) % tabs.length;
                        handleSelectTab(tabs[nextIndex].id);
                      }
                    }}
                    className="h-6 w-6 flex items-center justify-center hover:bg-muted rounded-sm"
                    aria-label="Next tab"
                  >
                    <ChevronRight className="h-3.5 w-3.5 text-muted-foreground" />
                  </button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Next tab (Ctrl+Tab)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        )}
      </div>
    </DndProvider>
  );
};

export default TabsBar;
