// Block-based storage type definitions for the auto-save system
import { Block, PartialBlock } from '@blocknote/core';

export interface BlockMetadata {
  id: string;
  type: string;
  lastModified: string;
  checksum?: string; // For content verification
}

export interface DocumentBlocksMetadata {
  version: string;
  documentId: string;
  blockOrder: string[]; // Array of block IDs in order
  lastModified: string;
  totalBlocks: number;
}

export interface BlockSaveOperation {
  blockId: string;
  content: Block;
  operation: 'create' | 'update' | 'delete';
}

export interface BlockLoadResult {
  blockId: string;
  content: Block | null;
  success: boolean;
  error?: string;
}

export interface BlockBatchSaveResult {
  documentId: string;
  savedBlocks: string[];
  failedBlocks: { blockId: string; error: string }[];
  success: boolean;
}

// Extended file storage API for block operations
export interface BlockFileStorageAPI {
  // Block-level operations
  saveDocumentBlocks: (
    documentId: string,
    blocks: BlockSaveOperation[]
  ) => Promise<BlockBatchSaveResult>;

  loadDocumentBlocks: (
    documentId: string,
    blockIds?: string[]
  ) => Promise<BlockLoadResult[]>;

  deleteDocumentBlocks: (
    documentId: string,
    blockIds: string[]
  ) => Promise<{ deletedBlocks: string[]; failedBlocks: string[] }>;

  // Document-level metadata operations
  saveDocumentBlocksMetadata: (
    documentId: string,
    metadata: DocumentBlocksMetadata
  ) => Promise<void>;

  loadDocumentBlocksMetadata: (
    documentId: string
  ) => Promise<DocumentBlocksMetadata | null>;

  // Migration utilities
  migrateDocumentToBlocks: (documentId: string) => Promise<boolean>;
  isDocumentBlockBased: (documentId: string) => Promise<boolean>;
}
