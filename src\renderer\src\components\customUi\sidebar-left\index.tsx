// Export the main component
export { default } from './HoverSidebarLeft';

// Export individual components for potential reuse
export { FileTypeIcon } from './components/FileTypeIcon';
export { Modal } from './components/Modal';
export { Dropdown } from './components/Dropdown';
export { RootDropArea } from './components/RootDropArea';
export { SearchInput } from './components/SearchInput';
export { TabButtons } from './components/TabButtons';
export { SidebarHeader } from './components/SidebarHeader';
export { SidebarContent } from './components/SidebarContent';
export { NodeItem } from './components/NodeItem';

// Export types
export type {
  FileNode,
  SidebarProps,
  SelectedTab,
  SortOrder,
  ModalProps,
  DropdownProps,
  NodeItemProps,
  SidebarHeaderProps,
  SearchInputProps,
  TabButtonsProps,
  SidebarContentProps,
  RootDropAreaProps
} from './types';

// Export utilities
export { cn } from './utils';

// Export custom hook
export { useSidebarState } from './hooks/useSidebarState';
