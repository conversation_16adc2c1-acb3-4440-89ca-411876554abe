"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { Popover, PopoverContent, PopoverTrigger } from "./popover"
import { Button } from "./button"

interface PopoverConfirmProps {
  isOpen: boolean
  onConfirm: () => void
  onCancel: () => void
  message?: string
  position?: 'top' | 'bottom' | 'left' | 'right'
  className?: string
  children?: React.ReactNode
}

export function PopoverConfirm({
  isOpen,
  onConfirm,
  onCancel,
  message = "Are you sure?",
  position = 'bottom',
  className,
  children
}: PopoverConfirmProps) {
  // Convert position to align and side for Radix Popover
  const getPopoverPosition = () => {
    switch (position) {
      case 'top': return { side: 'top', align: 'center' };
      case 'bottom': return { side: 'bottom', align: 'center' };
      case 'left': return { side: 'left', align: 'center' };
      case 'right': return { side: 'right', align: 'center' };
      default: return { side: 'bottom', align: 'center' };
    }
  };

  const { side, align } = getPopoverPosition();
    // If using this component directly with open state
  if (!children) {
    return isOpen ? (
      <Popover open={isOpen} onOpenChange={(open) => !open && onCancel()}>
        <PopoverContent
          className={cn("p-3 w-auto min-w-[200px]", className)}
          side={side as "top" | "right" | "bottom" | "left"}
          align={align as "start" | "center" | "end"}
        >
          <p className="text-sm text-center mb-3">{message}</p>
          <div className="flex justify-between gap-2">
            <Button variant="outline" size="sm" onClick={onCancel} className="flex-1">
              Cancel
            </Button>
            <Button variant="destructive" size="sm" onClick={onConfirm} className="flex-1">
              Delete
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    ) : null;
  }

  // With trigger (standard usage)
  return (
    <Popover open={isOpen} onOpenChange={(open) => !open && onCancel()}>
      <PopoverTrigger asChild>
        {children}
      </PopoverTrigger>
      <PopoverContent
        className={cn("p-3 w-auto min-w-[200px]", className)}
        side={side as "top" | "right" | "bottom" | "left"}
        align={align as "start" | "center" | "end"}
      >
        <p className="text-sm text-center mb-3">{message}</p>
        <div className="flex justify-between gap-2">
          <Button variant="outline" size="sm" onClick={onCancel} className="flex-1">
            Cancel
          </Button>
          <Button variant="destructive" size="sm" onClick={onConfirm} className="flex-1">
            Delete
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
