/**
 * Centralized theme + logo color constants.
 * Dark mode: near-black with a hint of midnight blue.
 * Light mode: pure white with deep neutral foreground.
 */

export const THEME_STORAGE_KEY = 'theme';
export const APP_LOGO_COLORS_STORAGE_KEY = 'appLogoCurrentColors';
export const APP_LOGO_COLOR_UPDATED_EVENT = 'appLogoColorUpdated'; // May become obsolete

/** Stroke colors that harmonize with the new theme */
export const LIGHT_MODE_STROKE_COLOR = '#d1d5db'; // gray-300, matches light mode border
export const DARK_MODE_STROKE_COLOR = '#4b5563'; // gray-600, matches dark mode border

/** A pair of hex colors for logo body + eyes */
export interface ColorScheme {
  body: string;
  eyes: string;
}

/** Clean, minimal palettes for light mode logos */
export const lightModeColorSchemes: ColorScheme[] = [
  { body: '#ffffff', eyes: '#1f2937' }, // Pure white / dark gray
  { body: '#f9fafb', eyes: '#374151' }, // Very light gray / medium dark gray
  { body: '#f3f4f6', eyes: '#4b5563' }, // Light gray / medium gray
  { body: '#e5e7eb', eyes: '#6b7280' }, // Lighter gray / darker gray
  { body: '#d1d5db', eyes: '#9ca3af' }, // Medium gray / light gray
  { body: '#f3f4f6', eyes: '#0070F3' }, // Light gray / vibrant blue accent
];

/** Sophisticated, dark palettes for dark mode logos */
export const darkModeColorSchemes: ColorScheme[] = [
  { body: '#111827', eyes: '#d1d5db' }, // Very dark gray / light gray
  { body: '#1f2937', eyes: '#9ca3af' }, // Dark gray / medium gray
  { body: '#374151', eyes: '#6b7280' }, // Medium dark gray / darker gray
  { body: '#4b5563', eyes: '#9ca3af' }, // Medium gray / medium gray
  { body: '#6b7280', eyes: '#d1d5db' }, // Lighter gray / light gray
  { body: '#1f2937', eyes: '#0070F3' }, // Dark gray / vibrant blue accent
];

/** Computed logo colors after palette + stroke are chosen */
export interface LogoColors {
  fill: string;   // logo body
  stroke: string; // outline / stroke
  eyes: string;   // accent details
}