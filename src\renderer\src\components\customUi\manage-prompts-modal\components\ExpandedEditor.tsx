import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '@/ui/dialog';
import { Button } from '@/ui/button';
import { Textarea } from '@/ui/textarea';
import { ExpandedEditorProps } from '../types';

export const ExpandedEditor: React.FC<ExpandedEditorProps> = ({
  isOpen,
  onClose,
  prompt,
  onUpdateContent
}) => {
  const [currentContent, setCurrentContent] = useState('');

  useEffect(() => {
    if (prompt) {
      setCurrentContent(prompt.content);
    }
  }, [prompt]);

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    setCurrentContent(newContent);

    if (prompt) {
      // Debounce or save on blur might be better here for performance
      onUpdateContent({ ...prompt, content: newContent }, newContent);
    }
  };

  if (!prompt) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl h-[70vh] flex flex-col p-0 gap-0">
        <DialogHeader className="p-4 border-b">
          <DialogTitle className='text-foreground'>Edit: {prompt.name}</DialogTitle>
          <DialogDescription className='text-foreground'>
            Modify the content of the prompt. Changes are saved automatically.
          </DialogDescription>
        </DialogHeader>
        <div className="p-4 flex-grow overflow-y-auto">
          <Textarea
            value={currentContent}
            onChange={handleContentChange}
            className="w-full h-full resize-none border rounded-md p-2 text-sm text-text-foreground focus:ring-1 focus:ring-ring prompt-content-scrollbar bg-input"
            placeholder="Enter prompt content..."
          />
        </div>
        <DialogFooter className="p-4">
          <Button onClick={onClose}>Done</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
