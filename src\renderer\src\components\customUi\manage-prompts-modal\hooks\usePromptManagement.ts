import { useState, useEffect, useCallback, useRef } from 'react';
import { nanoid } from 'nanoid';
import { ProjectSettings, DocumentSettings, SystemPrompt } from '@/types/global';
import { useNotification } from '@/contexts/NotificationContext';

interface UsePromptManagementProps {
  isOpen: boolean;
  projectSettings?: ProjectSettings;
  activeDocumentSettings?: DocumentSettings | null;
  activeDocumentId: string | null;
  onUpdateProjectSettings?: (updatedSettings: ProjectSettings) => Promise<void>;
  onUpdateActiveDocumentSettings?: (updatedSettings: DocumentSettings) => Promise<void>;
}

export const usePromptManagement = ({
  isOpen,
  projectSettings,
  activeDocumentSettings,
  activeDocumentId,
  onUpdateProjectSettings,
  onUpdateActiveDocumentSettings
}: UsePromptManagementProps) => {
  const { showNotification } = useNotification();

  // State
  const [activeTab, setActiveTab] = useState('global');
  const [editedGlobalPrompts, setEditedGlobalPrompts] = useState<SystemPrompt[]>([]);
  const [editedLocalPrompts, setEditedLocalPrompts] = useState<SystemPrompt[]>([]);
  const [newGlobalPromptName, setNewGlobalPromptName] = useState('');
  const [newLocalPromptName, setNewLocalPromptName] = useState('');
  const [promptToDelete, setPromptToDelete] = useState<SystemPrompt | null>(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [expandedEditorOpen, setExpandedEditorOpen] = useState(false);
  const [promptToEdit, setPromptToEdit] = useState<SystemPrompt | null>(null);

  const saveAllPendingChangesRef = useRef<() => Promise<void>>();

  // Initialize state when modal opens
  useEffect(() => {
    if (isOpen) {
      setEditedGlobalPrompts(
        projectSettings?.systemPrompts
          ? projectSettings.systemPrompts.map(p => ({ ...p, scope: 'global' as const, parentPromptId: p.parentPromptId || undefined }))
          : []
      );

      if (activeDocumentSettings?.localSystemPrompts) {
        setEditedLocalPrompts(
          activeDocumentSettings.localSystemPrompts.map(p => ({
            ...p,
            scope: 'local' as const,
            parentPromptId: p.parentPromptId || undefined
          }))
        );
      } else {
        setEditedLocalPrompts([]);
      }
    } else {
      setPromptToDelete(null);
    }
  }, [isOpen, projectSettings, activeDocumentSettings]);

  // Debounce setup for content updates
  const DEBOUNCE_DELAY = 500; // milliseconds
  const debounceTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const executePromptUpdateRef = useRef(async (promptToUpdate: SystemPrompt, newContent: string) => {});

  // Effect to keep executePromptUpdateRef.current up-to-date with latest state closures
  useEffect(() => {
    executePromptUpdateRef.current = async (promptToUpdate: SystemPrompt, newContent: string) => {
      const isLocalPrompt = promptToUpdate.scope === 'local';
      const originalContentForThisPrompt = promptToUpdate.content; // Content before this edit attempt

      if (isLocalPrompt) {
        // Calculate the next state based on the current editedLocalPrompts from the closure
        const updatedPrompts = editedLocalPrompts.map(p =>
          p.id === promptToUpdate.id ? { ...p, content: newContent } : p
        );
        setEditedLocalPrompts(updatedPrompts); // Update UI state

        try {
          if (activeDocumentSettings && onUpdateActiveDocumentSettings) {
            await onUpdateActiveDocumentSettings({
              ...activeDocumentSettings,
              localSystemPrompts: updatedPrompts.map(({ scope, ...rest }) => rest)
            });
            showNotification("Local prompt changes saved.", "success");
          }
        } catch (error) {
          console.error("Error saving local prompt:", error);
          showNotification("Failed to save local prompt. Reverting changes.", "error");
          // Revert only the specific prompt that failed, using functional update
          setEditedLocalPrompts(prev => prev.map(p =>
            p.id === promptToUpdate.id ? { ...p, content: originalContentForThisPrompt } : p
          ));
        }
      } else { // Global prompt
        // Calculate the next state based on the current editedGlobalPrompts from the closure
        const updatedPrompts = editedGlobalPrompts.map(p =>
          p.id === promptToUpdate.id ? { ...p, content: newContent } : p
        );
        setEditedGlobalPrompts(updatedPrompts); // Update UI state

        try {
          if (projectSettings && onUpdateProjectSettings) {
            await onUpdateProjectSettings({
              ...projectSettings,
              systemPrompts: updatedPrompts.map(({ scope, ...rest }) => rest)
            });
            showNotification("Global prompt changes saved.", "success");
          }
        } catch (error) {
          console.error("Error saving global prompt:", error);
          showNotification("Failed to save global prompt. Reverting changes.", "error");
          // Revert only the specific prompt that failed, using functional update
          setEditedGlobalPrompts(prev => prev.map(p =>
            p.id === promptToUpdate.id ? { ...p, content: originalContentForThisPrompt } : p
          ));
        }
      }
    };
  }, [
    editedLocalPrompts,
    editedGlobalPrompts,
    activeDocumentSettings,
    projectSettings,
    onUpdateActiveDocumentSettings,
    onUpdateProjectSettings,
    showNotification,
    setEditedLocalPrompts,
    setEditedGlobalPrompts
  ]);

  // Content update handler (debounced)
  const handleUpdatePromptContent = useCallback((promptToUpdate: SystemPrompt, newContent: string): Promise<void> => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    return new Promise((resolve) => {
      debounceTimerRef.current = setTimeout(async () => {
        await executePromptUpdateRef.current(promptToUpdate, newContent);
        resolve();
      }, DEBOUNCE_DELAY);
    });
  }, []); // Stable callback, no dependencies

  // Cleanup effect for the debounce timer
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // Name update handler
  const handleUpdatePromptName = useCallback(async (promptToUpdate: SystemPrompt, newName: string) => {
    const isLocalPrompt = promptToUpdate.scope === 'local';
    const originalName = promptToUpdate.name;

    if (isLocalPrompt) {
      const updatedLocalPrompts = editedLocalPrompts.map(p =>
        p.id === promptToUpdate.id ? { ...p, name: newName } : p
      );

      setEditedLocalPrompts(updatedLocalPrompts);

      try {
        if (activeDocumentSettings && onUpdateActiveDocumentSettings) {
          await onUpdateActiveDocumentSettings({
            ...activeDocumentSettings,
            localSystemPrompts: updatedLocalPrompts.map(p => {
              const { scope, ...rest } = p;
              return rest;
            })
          });
          showNotification(`Prompt renamed to "${newName}"`, "success");
        }
      } catch (error) {
        console.error("Error renaming local prompt:", error);
        showNotification("Failed to rename local prompt.", "error");
        setEditedLocalPrompts(editedLocalPrompts);
      }
    } else {
      const updatedGlobalPrompts = editedGlobalPrompts.map(p =>
        p.id === promptToUpdate.id ? { ...p, name: newName } : p
      );

      setEditedGlobalPrompts(updatedGlobalPrompts);

      try {
        if (projectSettings && onUpdateProjectSettings) {
          await onUpdateProjectSettings({
            ...projectSettings,
            systemPrompts: updatedGlobalPrompts.map(p => {
              const { scope, ...rest } = p;
              return rest;
            })
          });
          showNotification(`Prompt renamed to "${newName}"`, "success");
        }
      } catch (error) {
        console.error("Error renaming prompt:", error);
        showNotification("Failed to rename prompt.", "error");
        setEditedGlobalPrompts(editedGlobalPrompts);
      }
    }
  }, [editedLocalPrompts, editedGlobalPrompts, activeDocumentSettings, projectSettings, onUpdateActiveDocumentSettings, onUpdateProjectSettings, showNotification]);

  // Create new global prompt
  const handleCreateNewGlobalPrompt = useCallback(async () => {
    if (!newGlobalPromptName.trim()) {
      showNotification("Prompt name cannot be empty.", "error");
      return;
    }

    const newGlobalPrompt: SystemPrompt = {
      id: nanoid(),
      name: newGlobalPromptName.trim(),
      content: `Writing Style Instructions:
1. Use a professional tone in all responses.
2. Ensure clarity, conciseness, and precision in all explanations.
3. Maintain a neutral, factual, and objective perspective.
4. Use well-structured formatting to enhance readability.
Response Format Instructions:
• Utilize bold, italics, and other text styling for emphasis and clarity.
• Use ordered (1., 2., 3.) and unordered (-) lists where appropriate.
• Include section headings (##, ###) to structure responses logically.
• Use tables where necessary for comparisons and data presentation.
• Avoid conversational filler—provide only the necessary response.
Here is my text that you will work on:`,
      scope: 'global'
    };

    const updatedSystemPrompts = [...editedGlobalPrompts, newGlobalPrompt];
    setEditedGlobalPrompts(updatedSystemPrompts);

    try {
      if (projectSettings && onUpdateProjectSettings) {
        await onUpdateProjectSettings({
          ...projectSettings,
          systemPrompts: updatedSystemPrompts.map(p => {
            const { scope, ...rest } = p;
            return rest;
          })
        });
        showNotification(`Created global prompt: ${newGlobalPrompt.name}`, "success");
        setNewGlobalPromptName('');
      }
    } catch (error) {
      console.error("Error creating prompt:", error);
      showNotification("Failed to create prompt.", "error");
      setEditedGlobalPrompts(editedGlobalPrompts);
    }
  }, [newGlobalPromptName, editedGlobalPrompts, projectSettings, onUpdateProjectSettings, showNotification]);

  // Create new local prompt
  const handleCreateNewLocalPrompt = useCallback(async () => {
    if (!activeDocumentId || !activeDocumentSettings) {
      showNotification("No active document selected.", "error");
      return;
    }

    if (!newLocalPromptName.trim()) {
      showNotification("Prompt name cannot be empty.", "error");
      return;
    }

    const newLocalPrompt: SystemPrompt = {
      id: nanoid(),
      name: newLocalPromptName.trim(),
      content: `Document-specific instructions:
1. This prompt applies only to this document.
2. Customize these instructions for your specific document needs.
3. Local prompts can be used to create document-specific AI behaviors.`,
      scope: 'local'
    };

    const updatedLocalPrompts = [...editedLocalPrompts, newLocalPrompt];
    setEditedLocalPrompts(updatedLocalPrompts);

    try {
      if (onUpdateActiveDocumentSettings) {
        await onUpdateActiveDocumentSettings({
          ...activeDocumentSettings,
          localSystemPrompts: updatedLocalPrompts.map(p => {
            const { scope, ...rest } = p;
            return rest;
          })
        });
        showNotification(`Created local prompt: ${newLocalPrompt.name}`, "success");
        setNewLocalPromptName('');
        setActiveTab('document');
      }
    } catch (error) {
      console.error("Error creating local prompt:", error);
      showNotification("Failed to create local prompt.", "error");
      setEditedLocalPrompts(editedLocalPrompts);
    }
  }, [activeDocumentId, activeDocumentSettings, newLocalPromptName, editedLocalPrompts, onUpdateActiveDocumentSettings, showNotification]);

  // Save all pending changes
  const saveAllPendingChanges = useCallback(async () => {
    let hasUpdates = false;

    for (const prompt of editedGlobalPrompts) {
      const originalPrompt = projectSettings?.systemPrompts?.find(p => p.id === prompt.id);
      if (originalPrompt && originalPrompt.content !== prompt.content) {
        await handleUpdatePromptContent(prompt, prompt.content);
        hasUpdates = true;
      }
    }

    if (activeDocumentSettings) {
      for (const prompt of editedLocalPrompts) {
        const originalPrompt = activeDocumentSettings.localSystemPrompts?.find(p => p.id === prompt.id);
        if (originalPrompt && originalPrompt.content !== prompt.content) {
          await handleUpdatePromptContent(prompt, prompt.content);
          hasUpdates = true;
        }
      }
    }

    if (hasUpdates) {
      showNotification('All changes saved automatically', 'success');
    }
  }, [editedGlobalPrompts, editedLocalPrompts, projectSettings, activeDocumentSettings, handleUpdatePromptContent, showNotification]);

  // Update ref
  useEffect(() => {
    saveAllPendingChangesRef.current = saveAllPendingChanges;
  }, [saveAllPendingChanges]);

  return {
    // State
    activeTab,
    setActiveTab,
    editedGlobalPrompts,
    setEditedGlobalPrompts,
    editedLocalPrompts,
    setEditedLocalPrompts,
    newGlobalPromptName,
    setNewGlobalPromptName,
    newLocalPromptName,
    setNewLocalPromptName,
    promptToDelete,
    setPromptToDelete,
    isDeleteConfirmOpen,
    setIsDeleteConfirmOpen,
    expandedEditorOpen,
    setExpandedEditorOpen,
    promptToEdit,
    setPromptToEdit,

    // Handlers
    handleUpdatePromptContent,
    handleUpdatePromptName,
    handleCreateNewGlobalPrompt,
    handleCreateNewLocalPrompt,
    saveAllPendingChanges,
    saveAllPendingChangesRef
  };
};
