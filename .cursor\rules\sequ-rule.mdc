---
description:
globs:
alwaysApply: true
---
- if you do not recieve hyperclear directions then use sequential thinking MCP
- use shadcn and tailwind 4 when possible, install shadcn components instead of writing your own custom components when possible
- use the latest best practices 2025 for react and electron and vite
- build elegant code that solves things elegantly and efficiently
- always read the whole files and find relevant other files and read them to problem solve
- never get stuck planning and overthinking loops, always code before the end unless instructed otherwise
