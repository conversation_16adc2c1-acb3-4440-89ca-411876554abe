import { Block, BlockNoteEditor } from '@blocknote/core';

export interface FindMatch {
  blockId: string;
  blockIndex: number;
  textIndex: number;
  length: number;
  text: string;
  blockText: string;
}

export interface FindResult {
  matches: FindMatch[];
  currentMatchIndex: number;
  totalMatches: number;
}

/**
 * Extract text content from a BlockNote block
 */
export function extractTextFromBlock(block: Block): string {
  if (!block.content || !Array.isArray(block.content)) {
    return '';
  }

  return block.content
    .filter(item => item.type === 'text')
    .map(item => (item as any).text || '')
    .join('');
}

/**
 * Extract all text content from BlockNote document
 */
export function extractAllTextFromDocument(blocks: Block[]): string {
  return blocks
    .map(block => extractTextFromBlock(block))
    .join('\n');
}

/**
 * Find all matches of a search term in the document
 */
export function findInDocument(
  blocks: Block[],
  searchTerm: string,
  caseSensitive: boolean = false
): FindMatch[] {
  if (!searchTerm.trim()) {
    return [];
  }

  const matches: FindMatch[] = [];
  const searchText = caseSensitive ? searchTerm : searchTerm.toLowerCase();

  blocks.forEach((block, blockIndex) => {
    const blockText = extractTextFromBlock(block);
    if (!blockText) return;

    const textToSearch = caseSensitive ? blockText : blockText.toLowerCase();
    let startIndex = 0;

    while (true) {
      const foundIndex = textToSearch.indexOf(searchText, startIndex);
      if (foundIndex === -1) break;

      matches.push({
        blockId: block.id,
        blockIndex,
        textIndex: foundIndex,
        length: searchTerm.length,
        text: blockText.substring(foundIndex, foundIndex + searchTerm.length),
        blockText
      });

      startIndex = foundIndex + 1;
    }
  });

  return matches;
}

/**
 * Navigate to a specific match in the editor
 */
export function navigateToMatch(
  editor: BlockNoteEditor,
  match: FindMatch
): void {
  try {
    // Find the block by ID
    const block = editor.document.find(b => b.id === match.blockId);
    if (!block) {
      console.warn('Block not found for match:', match.blockId);
      return;
    }

    // Set cursor position to the beginning of the match
    editor.setTextCursorPosition(block, match.textIndex);

    // Try to scroll the block into view
    const blockElement = document.querySelector(`[data-id="${match.blockId}"]`);
    if (blockElement) {
      blockElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  } catch (error) {
    console.error('Error navigating to match:', error);
  }
}

// Store original block states for restoration
const originalBlockStates = new Map<string, any>();

/**
 * Highlight all matches in the editor using BlockNote's native background color
 */
export function highlightAllMatches(
  editor: BlockNoteEditor,
  matches: FindMatch[],
  currentMatchIndex: number = -1
): void {
  try {
    // Clear existing highlights first
    clearAllHighlights(editor);

    // Store original states and apply highlights
    matches.forEach((match, index) => {
      const block = editor.document.find(b => b.id === match.blockId);
      if (!block) return;

      // Store original state if not already stored
      if (!originalBlockStates.has(match.blockId)) {
        originalBlockStates.set(match.blockId, JSON.parse(JSON.stringify(block)));
      }

      // Apply highlighting using BlockNote's background color
      const isCurrent = index === currentMatchIndex;
      highlightTextInBlock(editor, block, match, isCurrent);
    });
  } catch (error) {
    console.error('Error highlighting matches:', error);
  }
}

/**
 * Clear all search highlights by restoring original block states
 */
export function clearAllHighlights(editor: BlockNoteEditor): void {
  try {
    // Restore all blocks to their original states
    originalBlockStates.forEach((originalBlock, blockId) => {
      const currentBlock = editor.document.find(b => b.id === blockId);
      if (currentBlock) {
        editor.updateBlock(currentBlock, originalBlock);
      }
    });

    // Clear the stored states
    originalBlockStates.clear();
  } catch (error) {
    console.error('Error clearing highlights:', error);
  }
}

/**
 * Highlight text within a specific block using BlockNote's background color
 */
function highlightTextInBlock(
  editor: BlockNoteEditor,
  block: any,
  match: FindMatch,
  isCurrent: boolean = false
): void {
  try {
    if (!block.content || !Array.isArray(block.content)) return;

    let currentOffset = 0;
    let matchProcessed = false;

    // Create new content with highlighted text
    const newContent = block.content.map((item: any) => {
      if (item.type === 'text' && item.text && !matchProcessed) {
        const text = item.text;
        const itemStart = currentOffset;
        const itemEnd = currentOffset + text.length;

        // Check if this text item contains our match
        if (match.textIndex >= itemStart && match.textIndex < itemEnd) {
          matchProcessed = true;

          // Calculate relative positions within this text item
          const relativeStart = match.textIndex - itemStart;
          const relativeEnd = relativeStart + match.length;

          // Split the text around the match
          const beforeMatch = text.substring(0, relativeStart);
          const matchText = text.substring(relativeStart, relativeEnd);
          const afterMatch = text.substring(relativeEnd);

          // Create array of text items with highlighting
          const textItems = [];

          if (beforeMatch) {
            textItems.push({
              type: 'text',
              text: beforeMatch,
              styles: item.styles || {}
            });
          }

          // Add highlighted match
          textItems.push({
            type: 'text',
            text: matchText,
            styles: {
              ...(item.styles || {}),
              backgroundColor: isCurrent ? 'orange' : 'yellow' // Use BlockNote color names
            }
          });

          if (afterMatch) {
            textItems.push({
              type: 'text',
              text: afterMatch,
              styles: item.styles || {}
            });
          }

          return textItems;
        }

        currentOffset += text.length;
      }
      return item;
    }).flat();

    // Update the block with highlighted content
    editor.updateBlock(block, {
      ...block,
      content: newContent
    });
  } catch (error) {
    console.error('Error highlighting text in block:', error);
  }
}

/**
 * Remove highlights when text changes and no longer matches
 */
export function cleanupInvalidHighlights(
  editor: BlockNoteEditor,
  searchTerm: string,
  caseSensitive: boolean = false
): void {
  try {
    const searchText = caseSensitive ? searchTerm : searchTerm.toLowerCase();

    originalBlockStates.forEach((originalBlock, blockId) => {
      const currentBlock = editor.document.find(b => b.id === blockId);
      if (!currentBlock) return;

      const currentText = extractTextFromBlock(currentBlock);
      const textToSearch = caseSensitive ? currentText : currentText.toLowerCase();

      // If the block no longer contains the search term, restore it
      if (!textToSearch.includes(searchText)) {
        editor.updateBlock(currentBlock, originalBlock);
        originalBlockStates.delete(blockId);
      }
    });
  } catch (error) {
    console.error('Error cleaning up invalid highlights:', error);
  }
}

/**
 * Create a find result object
 */
export function createFindResult(
  matches: FindMatch[],
  currentIndex: number = 0
): FindResult {
  return {
    matches,
    currentMatchIndex: Math.max(0, Math.min(currentIndex, matches.length - 1)),
    totalMatches: matches.length
  };
}

/**
 * Get the next match index (wraps around)
 */
export function getNextMatchIndex(currentIndex: number, totalMatches: number): number {
  if (totalMatches === 0) return -1;
  return (currentIndex + 1) % totalMatches;
}

/**
 * Get the previous match index (wraps around)
 */
export function getPreviousMatchIndex(currentIndex: number, totalMatches: number): number {
  if (totalMatches === 0) return -1;
  return currentIndex <= 0 ? totalMatches - 1 : currentIndex - 1;
}

/**
 * Replace a specific match in the document
 */
export function replaceInDocument(
  editor: BlockNoteEditor,
  match: FindMatch,
  replacementText: string
): void {
  try {
    // Find the block by ID
    const block = editor.document.find(b => b.id === match.blockId);
    if (!block) {
      console.warn('Block not found for replacement:', match.blockId);
      return;
    }

    // Get the current block content
    if (!block.content || !Array.isArray(block.content)) {
      console.warn('Block has no content array:', match.blockId);
      return;
    }

    // Create a new content array with the replacement
    const newContent = block.content.map(item => {
      if (item.type === 'text' && (item as any).text) {
        const text = (item as any).text;
        const beforeMatch = text.substring(0, match.textIndex);
        const afterMatch = text.substring(match.textIndex + match.length);

        // Only replace if this text item contains the match
        if (text.includes(match.text) &&
            text.substring(match.textIndex, match.textIndex + match.length) === match.text) {
          return {
            ...item,
            text: beforeMatch + replacementText + afterMatch
          };
        }
      }
      return item;
    });

    // Update the block with new content
    editor.updateBlock(block, {
      ...block,
      content: newContent
    });

    console.log('[findUtils] Replaced text in block:', match.blockId);
  } catch (error) {
    console.error('Error replacing text in document:', error);
  }
}

/**
 * Replace all matches in the document
 */
export function replaceAllInDocument(
  editor: BlockNoteEditor,
  matches: FindMatch[],
  replacementText: string
): void {
  try {
    // Group matches by block ID to batch updates
    const matchesByBlock = new Map<string, FindMatch[]>();

    matches.forEach(match => {
      if (!matchesByBlock.has(match.blockId)) {
        matchesByBlock.set(match.blockId, []);
      }
      matchesByBlock.get(match.blockId)!.push(match);
    });

    // Process each block
    matchesByBlock.forEach((blockMatches, blockId) => {
      const block = editor.document.find(b => b.id === blockId);
      if (!block || !block.content || !Array.isArray(block.content)) {
        console.warn('Block not found or has no content for replacement:', blockId);
        return;
      }

      // Sort matches by text index in descending order to replace from end to start
      // This prevents index shifting issues
      const sortedMatches = [...blockMatches].sort((a, b) => b.textIndex - a.textIndex);

      // Create new content with all replacements
      const newContent = block.content.map(item => {
        if (item.type === 'text' && (item as any).text) {
          let text = (item as any).text;

          // Apply all replacements for this text item
          sortedMatches.forEach(match => {
            if (text.substring(match.textIndex, match.textIndex + match.length) === match.text) {
              const beforeMatch = text.substring(0, match.textIndex);
              const afterMatch = text.substring(match.textIndex + match.length);
              text = beforeMatch + replacementText + afterMatch;
            }
          });

          return {
            ...item,
            text
          };
        }
        return item;
      });

      // Update the block
      editor.updateBlock(block, {
        ...block,
        content: newContent
      });
    });

    console.log('[findUtils] Replaced all matches:', matches.length);
  } catch (error) {
    console.error('Error replacing all matches in document:', error);
  }
}
