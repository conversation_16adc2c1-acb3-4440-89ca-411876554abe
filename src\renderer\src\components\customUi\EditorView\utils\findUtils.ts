import { Block, BlockNoteEditor } from '@blocknote/core';

export interface FindMatch {
  blockId: string;
  blockIndex: number;
  textIndex: number;
  length: number;
  text: string;
  blockText: string;
}

export interface FindResult {
  matches: FindMatch[];
  currentMatchIndex: number;
  totalMatches: number;
}

/**
 * Extract text content from a BlockNote block
 */
export function extractTextFromBlock(block: Block): string {
  if (!block.content || !Array.isArray(block.content)) {
    return '';
  }

  return block.content
    .filter(item => item.type === 'text')
    .map(item => (item as any).text || '')
    .join('');
}

/**
 * Extract all text content from BlockNote document
 */
export function extractAllTextFromDocument(blocks: Block[]): string {
  return blocks
    .map(block => extractTextFromBlock(block))
    .join('\n');
}

/**
 * Find all matches of a search term in the document
 */
export function findInDocument(
  blocks: Block[],
  searchTerm: string,
  caseSensitive: boolean = false
): FindMatch[] {
  if (!searchTerm.trim()) {
    return [];
  }

  const matches: FindMatch[] = [];
  const searchText = caseSensitive ? searchTerm : searchTerm.toLowerCase();

  blocks.forEach((block, blockIndex) => {
    const blockText = extractTextFromBlock(block);
    if (!blockText) return;

    const textToSearch = caseSensitive ? blockText : blockText.toLowerCase();
    let startIndex = 0;

    while (true) {
      const foundIndex = textToSearch.indexOf(searchText, startIndex);
      if (foundIndex === -1) break;

      matches.push({
        blockId: block.id,
        blockIndex,
        textIndex: foundIndex,
        length: searchTerm.length,
        text: blockText.substring(foundIndex, foundIndex + searchTerm.length),
        blockText
      });

      startIndex = foundIndex + 1;
    }
  });

  return matches;
}

/**
 * Navigate to a specific match in the editor
 */
export function navigateToMatch(
  editor: BlockNoteEditor,
  match: FindMatch
): void {
  try {
    // Find the block by ID
    const block = editor.document.find(b => b.id === match.blockId);
    if (!block) {
      console.warn('Block not found for match:', match.blockId);
      return;
    }

    // Set cursor position to the beginning of the match
    editor.setTextCursorPosition(block, match.textIndex);

    // Try to scroll the block into view
    const blockElement = document.querySelector(`[data-id="${match.blockId}"]`);
    if (blockElement) {
      blockElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  } catch (error) {
    console.error('Error navigating to match:', error);
  }
}

/**
 * Highlight all matches in the editor
 */
export function highlightAllMatches(
  matches: FindMatch[],
  currentMatchIndex: number = -1
): void {
  try {
    // Remove existing highlights
    clearAllHighlights();

    // Add a small delay to ensure DOM is ready
    setTimeout(() => {
      matches.forEach((match, index) => {
        const blockElement = document.querySelector(`[data-id="${match.blockId}"]`);
        if (!blockElement) return;

        // Find text nodes within the block
        const walker = document.createTreeWalker(
          blockElement,
          NodeFilter.SHOW_TEXT,
          null
        );

        let textNode;
        let currentOffset = 0;

        while (textNode = walker.nextNode()) {
          const nodeText = textNode.textContent || '';
          const nodeLength = nodeText.length;

          // Check if this text node contains our match
          if (currentOffset <= match.textIndex &&
              currentOffset + nodeLength > match.textIndex) {

            const relativeStart = match.textIndex - currentOffset;
            const relativeEnd = relativeStart + match.length;

            // Only highlight if the match is fully within this text node
            if (relativeEnd <= nodeLength) {
              highlightTextInNode(textNode as Text, relativeStart, relativeEnd, index === currentMatchIndex);
              break;
            }
          }

          currentOffset += nodeLength;
        }
      });
    }, 10);
  } catch (error) {
    console.error('Error highlighting matches:', error);
  }
}

/**
 * Clear all search highlights
 */
export function clearAllHighlights(): void {
  try {
    const highlights = document.querySelectorAll('.find-highlight, .find-highlight-current');
    highlights.forEach(highlight => {
      const parent = highlight.parentNode;
      if (parent) {
        parent.replaceChild(document.createTextNode(highlight.textContent || ''), highlight);
        parent.normalize();
      }
    });
  } catch (error) {
    console.error('Error clearing highlights:', error);
  }
}

/**
 * Highlight text within a specific text node
 */
function highlightTextInNode(
  textNode: Text,
  start: number,
  end: number,
  isCurrent: boolean = false
): void {
  try {
    const text = textNode.textContent || '';
    const beforeText = text.substring(0, start);
    const matchText = text.substring(start, end);
    const afterText = text.substring(end);

    const fragment = document.createDocumentFragment();

    if (beforeText) {
      fragment.appendChild(document.createTextNode(beforeText));
    }

    const highlight = document.createElement('span');
    highlight.className = isCurrent ? 'find-highlight-current' : 'find-highlight';
    highlight.style.backgroundColor = isCurrent ? '#ff6b35' : '#ffeb3b';
    highlight.style.color = isCurrent ? '#ffffff' : '#000000';
    highlight.style.padding = '1px 2px';
    highlight.style.borderRadius = '2px';
    highlight.style.fontWeight = isCurrent ? 'bold' : 'normal';
    highlight.textContent = matchText;
    fragment.appendChild(highlight);

    if (afterText) {
      fragment.appendChild(document.createTextNode(afterText));
    }

    textNode.parentNode?.replaceChild(fragment, textNode);
  } catch (error) {
    console.error('Error highlighting text in node:', error);
  }
}

/**
 * Create a find result object
 */
export function createFindResult(
  matches: FindMatch[],
  currentIndex: number = 0
): FindResult {
  return {
    matches,
    currentMatchIndex: Math.max(0, Math.min(currentIndex, matches.length - 1)),
    totalMatches: matches.length
  };
}

/**
 * Get the next match index (wraps around)
 */
export function getNextMatchIndex(currentIndex: number, totalMatches: number): number {
  if (totalMatches === 0) return -1;
  return (currentIndex + 1) % totalMatches;
}

/**
 * Get the previous match index (wraps around)
 */
export function getPreviousMatchIndex(currentIndex: number, totalMatches: number): number {
  if (totalMatches === 0) return -1;
  return currentIndex <= 0 ? totalMatches - 1 : currentIndex - 1;
}

/**
 * Replace a specific match in the document
 */
export function replaceInDocument(
  editor: BlockNoteEditor,
  match: FindMatch,
  replacementText: string
): void {
  try {
    // Find the block by ID
    const block = editor.document.find(b => b.id === match.blockId);
    if (!block) {
      console.warn('Block not found for replacement:', match.blockId);
      return;
    }

    // Get the current block content
    if (!block.content || !Array.isArray(block.content)) {
      console.warn('Block has no content array:', match.blockId);
      return;
    }

    // Create a new content array with the replacement
    const newContent = block.content.map(item => {
      if (item.type === 'text' && (item as any).text) {
        const text = (item as any).text;
        const beforeMatch = text.substring(0, match.textIndex);
        const afterMatch = text.substring(match.textIndex + match.length);

        // Only replace if this text item contains the match
        if (text.includes(match.text) &&
            text.substring(match.textIndex, match.textIndex + match.length) === match.text) {
          return {
            ...item,
            text: beforeMatch + replacementText + afterMatch
          };
        }
      }
      return item;
    });

    // Update the block with new content
    editor.updateBlock(block, {
      ...block,
      content: newContent
    });

    console.log('[findUtils] Replaced text in block:', match.blockId);
  } catch (error) {
    console.error('Error replacing text in document:', error);
  }
}

/**
 * Replace all matches in the document
 */
export function replaceAllInDocument(
  editor: BlockNoteEditor,
  matches: FindMatch[],
  replacementText: string
): void {
  try {
    // Group matches by block ID to batch updates
    const matchesByBlock = new Map<string, FindMatch[]>();

    matches.forEach(match => {
      if (!matchesByBlock.has(match.blockId)) {
        matchesByBlock.set(match.blockId, []);
      }
      matchesByBlock.get(match.blockId)!.push(match);
    });

    // Process each block
    matchesByBlock.forEach((blockMatches, blockId) => {
      const block = editor.document.find(b => b.id === blockId);
      if (!block || !block.content || !Array.isArray(block.content)) {
        console.warn('Block not found or has no content for replacement:', blockId);
        return;
      }

      // Sort matches by text index in descending order to replace from end to start
      // This prevents index shifting issues
      const sortedMatches = [...blockMatches].sort((a, b) => b.textIndex - a.textIndex);

      // Create new content with all replacements
      const newContent = block.content.map(item => {
        if (item.type === 'text' && (item as any).text) {
          let text = (item as any).text;

          // Apply all replacements for this text item
          sortedMatches.forEach(match => {
            if (text.substring(match.textIndex, match.textIndex + match.length) === match.text) {
              const beforeMatch = text.substring(0, match.textIndex);
              const afterMatch = text.substring(match.textIndex + match.length);
              text = beforeMatch + replacementText + afterMatch;
            }
          });

          return {
            ...item,
            text
          };
        }
        return item;
      });

      // Update the block
      editor.updateBlock(block, {
        ...block,
        content: newContent
      });
    });

    console.log('[findUtils] Replaced all matches:', matches.length);
  } catch (error) {
    console.error('Error replacing all matches in document:', error);
  }
}
