import { renderHook, act } from '@testing-library/react-hooks';
import { useDocumentStore } from '../stores/blockDocumentStore';
import fileSystemStorage from '../lib/file-system-storage';

// Mock the fileSystemStorage module
jest.mock('../lib/file-system-storage', () => ({
  getStoragePath: jest.fn(),
  selectDirectory: jest.fn(),
  listFiles: jest.fn(),
  loadFile: jest.fn(),
  createDocumentInWorkspace: jest.fn(),
  createFolder: jest.fn(),
  deleteFile: jest.fn(),
  renameFile: jest.fn(),
  moveItem: jest.fn(),
  loadProjectSettings: jest.fn(),
  saveProjectSettings: jest.fn(),
  saveDocumentMetadata: jest.fn()
}));

describe('documentStore', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();

    // Reset the store state
    const { result } = renderHook(() => useDocumentStore());
    act(() => {
      result.current.setOpenFiles([]);
      result.current.setActiveFileId(null);
      result.current.setDetailedActiveFile(null);
      result.current.setFiles([]);
      result.current.setProjectSettings(null);
      result.current.setCurrentStoragePath(null);
    });
  });

  describe('openTab', () => {
    it('should add a file to openFiles and set it as active', async () => {
      const { result } = renderHook(() => useDocumentStore());
      const testFile = { id: 'test-id', name: 'test-file', type: 'document', parentId: null, path: '/test-file', documentSettings: null };

      await act(async () => {
        await result.current.openTab(testFile);
      });

      expect(result.current.openFiles).toContainEqual(testFile);
      expect(result.current.activeFileId).toBe('test-id');
      expect(result.current.detailedActiveFile).toEqual(testFile);
    });

    it('should not add a duplicate file to openFiles', async () => {
      const { result } = renderHook(() => useDocumentStore());
      const testFile = { id: 'test-id', name: 'test-file', type: 'document', parentId: null, path: '/test-file', documentSettings: null };

      await act(async () => {
        result.current.setOpenFiles([testFile]);
        await result.current.openTab(testFile);
      });

      expect(result.current.openFiles.length).toBe(1);
      expect(result.current.activeFileId).toBe('test-id');
    });
  });

  describe('closeTab', () => {
    it('should remove a file from openFiles', async () => {
      const { result } = renderHook(() => useDocumentStore());
      const testFile1 = { id: 'test-id-1', name: 'test-file-1', type: 'document', parentId: null, path: '/test-file-1', documentSettings: null };
      const testFile2 = { id: 'test-id-2', name: 'test-file-2', type: 'document', parentId: null, path: '/test-file-2', documentSettings: null };

      await act(async () => {
        result.current.setOpenFiles([testFile1, testFile2]);
        result.current.setActiveFileId('test-id-1');
        await result.current.closeTab('test-id-1');
      });

      expect(result.current.openFiles).toEqual([testFile2]);
      expect(result.current.activeFileId).toBe('test-id-2');
    });

    it('should set activeFileId to null if closing the last tab', async () => {
      const { result } = renderHook(() => useDocumentStore());
      const testFile = { id: 'test-id', name: 'test-file', type: 'document', parentId: null, path: '/test-file', documentSettings: null };

      await act(async () => {
        result.current.setOpenFiles([testFile]);
        result.current.setActiveFileId('test-id');
        await result.current.closeTab('test-id');
      });

      expect(result.current.openFiles).toEqual([]);
      expect(result.current.activeFileId).toBeNull();
      expect(result.current.detailedActiveFile).toBeNull();
    });
  });

  describe('selectTab', () => {
    it('should set the active file and load its details', async () => {
      const { result } = renderHook(() => useDocumentStore());
      const testFile1 = { id: 'test-id-1', name: 'test-file-1', type: 'document', parentId: null, path: '/test-file-1', documentSettings: null };
      const testFile2 = { id: 'test-id-2', name: 'test-file-2', type: 'document', parentId: null, path: '/test-file-2', documentSettings: null };

      // Mock loadFile to return testFile2
      (fileSystemStorage.loadFile as jest.Mock).mockResolvedValue(testFile2);

      await act(async () => {
        result.current.setOpenFiles([testFile1, testFile2]);
        result.current.setActiveFileId('test-id-1');
        await result.current.selectTab('test-id-2');
      });

      expect(result.current.activeFileId).toBe('test-id-2');
      expect(result.current.detailedActiveFile).toEqual(testFile2);
      expect(fileSystemStorage.loadFile).toHaveBeenCalledWith('test-id-2');
    });
  });
});
